"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import { FileText, Loader2, Plus, Clock, CheckCircle, FileEdit, MessageSquare, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { Separator } from "@/components/ui/separator"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { ContentCard } from "@/components/content-card"

export default function MyPostsPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // 立即檢查登入狀態並重定向
  useEffect(() => {
    // 如果已確定未登入，立即重定向
    if (!isLoading && !isAuthenticated) {
      router.replace("/auth/login") // 使用 replace 而不是 push 以避免瀏覽歷史堆疊
    }
  }, [isLoading, isAuthenticated, router])

  // 如果未登入或正在檢查登入狀態，顯示最小的加載指示器
  if (isLoading || !isAuthenticated) {
    return (
      <div className="flex justify-center items-center min-h-[80vh]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // 以下是原始的 MyPostsPage 內容，只有在確認用戶已登入後才會渲染
  // 為了簡化代碼，這裡省略了原始內容
  return <MyPostsContent />
}

// 將主要內容分離到單獨的組件，只有在用戶已登入時才會渲染
function MyPostsContent() {
  const { profile } = useAuth()
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("pending")
  const [activeSubTab, setActiveSubTab] = useState("viewpoints")
  const [isLoadingData, setIsLoadingData] = useState(true)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [draftToDelete, setDraftToDelete] = useState<{ id: number; type: "viewpoint" | "discussion" } | null>(null)

  // Read query parameters for tab selection
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const tabParam = params.get("tab")
    const subTabParam = params.get("subTab")

    if (tabParam && ["pending", "drafts", "published"].includes(tabParam)) {
      setActiveTab(tabParam)
    }

    if (subTabParam && ["viewpoints", "discussions"].includes(subTabParam)) {
      setActiveSubTab(subTabParam)
    }
  }, [])

  // 模擬獲取用戶發表的內容
  useEffect(() => {
    // 模擬 API 請求延遲
    const timer = setTimeout(() => {
      setIsLoadingData(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // 模擬數據 - 待審核觀點卡
  const pendingViewpoints = [
    {
      id: 101,
      contentType: "viewpoint" as const,
      semanticType: "insight" as const,
      title: "GPT-4o 與 Claude 3 Opus 的實測比較",
      content:
        "經過多次實測，我發現 GPT-4o 在創意寫作和多模態理解上表現更佳，而 Claude 3 Opus 在長文本分析和事實準確性方面略勝一籌。對於需要處理大量文本數據的企業應用，Claude 可能是更好的選擇；而對於需要創意內容生成的場景，GPT-4o 則更為適合。",
      topics: ["LLM"],
      subtopics: ["GPT-4o", "Claude 3", "模型比較"],
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      sourceType: "original" as const,
      timestamp: "2天前",
      stats: {
        likes: 0,
        dislikes: 0,
        comments: 0,
        bookmarks: 0,
      },
      status: "pending",
      submittedAt: "2023-06-15T10:30:00Z",
      variant: "grid",
      features: { truncate: true },
    },
    {
      id: 102,
      contentType: "viewpoint" as const,
      semanticType: "guide" as const,
      title: "使用 Mistral Large 構建企業知識庫的最佳實踐",
      content:
        "Mistral Large 在企業知識庫應用中表現出色，特別是在多語言環境下。本文分享了從數據準備、模型調優到部署的完整流程，以及如何解決常見的挑戰，如上下文長度限制和多輪對話中的記憶問題。",
      topics: ["企業AI"],
      subtopics: ["Mistral", "知識庫", "RAG"],
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      sourceType: "original" as const,
      timestamp: "3天前",
      stats: {
        likes: 0,
        dislikes: 0,
        comments: 0,
        bookmarks: 0,
      },
      status: "pending",
      submittedAt: "2023-06-14T09:15:00Z",
      variant: "grid",
      features: { truncate: true },
    },
  ]

  // 模擬數據 - 草稿觀點卡
  const draftViewpoints = [
    {
      id: 201,
      contentType: "viewpoint" as const,
      semanticType: "experience" as const,
      title: "使用 LoRA 微調 Llama 3 的實戰經驗",
      content:
        "最近嘗試使用 LoRA 技術微調 Llama 3 模型，發現幾個關鍵優化點。首先，參數設置對性能影響顯著，尤其是 rank 和 alpha 值的選擇。其次，訓練數據的質量比數量更重要，精心準備的少量高質量數據往往能帶來更好的效果。",
      topics: ["Fine-tuning"],
      subtopics: ["LoRA", "Llama 3", "參數優化"],
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      sourceType: "original" as const,
      status: "draft",
      timestamp: "草稿",
      stats: {
        likes: 0,
        dislikes: 0,
        comments: 0,
        bookmarks: 0,
      },
      lastSaved: "2023-06-10T15:45:00Z",
      variant: "grid",
      features: { truncate: true },
    },
    {
      id: 202,
      contentType: "viewpoint" as const,
      semanticType: "trap" as const,
      title: "RAG 系統中的常見陷阱與解決方案",
      content:
        "在構建 RAG 系統時，發現幾個容易被忽視的問題：檢索結果多樣性不足、上下文窗口限制和幻覺生成。通過實施語義去重、分塊策略優化和事實一致性檢查，可以顯著提升系統質量。",
      topics: ["RAG"],
      subtopics: ["檢索優化", "幻覺問題", "Debug"],
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      sourceType: "original" as const,
      status: "draft",
      timestamp: "草稿",
      stats: {
        likes: 0,
        dislikes: 0,
        comments: 0,
        bookmarks: 0,
      },
      lastSaved: "2023-06-05T09:20:00Z",
      variant: "grid",
      features: { truncate: true },
    },
  ]

  // 模擬數據 - 已發布觀點卡
  const publishedViewpoints = [
    {
      id: 301,
      contentType: "viewpoint" as const,
      semanticType: "concept" as const,
      title: "Sora 的技術原理與架構解析",
      content:
        "OpenAI 的 Sora 模型採用了擴散模型架構，但其創新之處在於時空潛在擴散模型的應用。這種方法將視頻視為四維數據（寬、高、通道、時間），使模型能夠生成連貫且高質量的視頻內容。",
      topics: ["Text-to-Video"],
      subtopics: ["Sora", "擴散模型", "視頻生成"],
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      sourceType: "original" as const,
      timestamp: "2週前",
      stats: {
        likes: 67,
        dislikes: 2,
        comments: 15,
        bookmarks: 41,
      },
      status: "published",
      publishedAt: "2023-05-20T14:30:00Z",
      variant: "grid",
      features: { truncate: true },
    },
    {
      id: 302,
      contentType: "viewpoint" as const,
      semanticType: "guide" as const,
      title: "使用 LangChain 構建高效 RAG 系統的完整指南",
      content:
        "本指南將帶你一步步構建一個高效的 RAG 系統。首先，我們需要選擇合適的向量數據庫，如 FAISS 或 Pinecone。然後，設計合理的文檔分塊策略，確保語義完整性。接著，實現檢索策略，包括混合檢索和重排序機制。",
      topics: ["RAG"],
      subtopics: ["LangChain", "向量數據庫", "檢索策略"],
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      sourceType: "original" as const,
      timestamp: "1個月前",
      stats: {
        likes: 56,
        dislikes: 1,
        comments: 14,
        bookmarks: 32,
      },
      status: "published",
      publishedAt: "2023-05-05T11:15:00Z",
      variant: "grid",
      features: { truncate: true },
    },
    {
      id: 303,
      contentType: "viewpoint" as const,
      semanticType: "insight" as const,
      title: "大型語言模型的幻覺問題：原因與緩解策略",
      content:
        "LLM 幻覺問題的根源在於訓練數據中的噪聲、模型參數量有限以及優化目標的局限性。通過引入外部知識、增強檢索機制和設計更好的提示，可以有效減少幻覺的發生。本文深入分析了各種緩解策略的優缺點。",
      topics: ["LLM"],
      subtopics: ["幻覺問題", "知識增強", "提示工程"],
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      sourceType: "original" as const,
      timestamp: "1個月前",
      stats: {
        likes: 89,
        dislikes: 3,
        comments: 27,
        bookmarks: 54,
      },
      status: "published",
      publishedAt: "2023-05-02T08:45:00Z",
      variant: "grid",
      features: { truncate: true },
    },
    {
      id: 304,
      contentType: "viewpoint" as const,
      semanticType: "experience" as const,
      title: "從零開始構建多模態 AI 助手的實踐經驗",
      content:
        "本文記錄了我從零開始構建一個支持文本、圖像和音頻輸入的多模態 AI 助手的全過程。包括模型選擇、數據處理、架構設計以及部署優化等方面的實踐經驗和踩坑記錄。",
      topics: ["多模態"],
      subtopics: ["AI助手", "系統設計", "部署優化"],
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      sourceType: "original" as const,
      timestamp: "2個月前",
      stats: {
        likes: 72,
        dislikes: 1,
        comments: 18,
        bookmarks: 39,
      },
      status: "published",
      publishedAt: "2023-04-15T16:20:00Z",
      variant: "grid",
      features: { truncate: true },
    },
  ]

  // 模擬數據 - 草稿討論
  const draftDiscussions = [
    {
      id: 401,
      contentType: "discussion" as const,
      title: "多模態模型在實際業務中的應用場景討論",
      content: "想和大家討論一下多模態模型在實際業務場景中的應用案例和最佳實踐...",
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      timestamp: "草稿",
      semanticType: "discussion",
      status: "draft",
      lastSaved: "2023-06-12T16:30:00Z",
      stats: {
        replies: 0,
        views: 0,
      },
      variant: "grid",
    },
    {
      id: 402,
      contentType: "discussion" as const,
      title: "開源 LLM 與閉源商業模型的性能差距正在縮小嗎？",
      content: "隨著 Llama 3、Mistral 等開源模型的發展，它們與 GPT-4 等閉源商業模型的差距似乎正在縮小...",
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      timestamp: "草稿",
      semanticType: "question",
      status: "draft",
      lastSaved: "2023-06-08T11:15:00Z",
      stats: {
        replies: 0,
        views: 0,
      },
      variant: "grid",
    },
  ]

  // 模擬數據 - 已發布討論
  const publishedDiscussions = [
    {
      id: 501,
      contentType: "discussion" as const,
      title: "如何有效評估 RAG 系統的檢索質量？",
      content: "最近在構建 RAG 系統時，遇到了評估檢索質量的問題...",
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      timestamp: "2週前",
      semanticType: "question",
      status: "published",
      publishedAt: "2023-05-15T10:45:00Z",
      stats: {
        replies: 27,
        views: 342,
        likes: 18,
        dislikes: 2,
      },
      variant: "grid",
    },
    {
      id: 502,
      contentType: "discussion" as const,
      title: "Prompt 工程師的職業發展路徑",
      content: "作為一名 Prompt 工程師，我想和大家討論一下這個職位的未來發展方向...",
      author: {
        id: profile?.id || "user1",
        name: profile?.name || "用戶",
      },
      timestamp: "1個月前",
      semanticType: "discussion",
      status: "published",
      publishedAt: "2023-04-28T09:20:00Z",
      stats: {
        replies: 24,
        views: 518,
        likes: 35,
        dislikes: 0,
      },
      variant: "grid",
    },
  ]

  // 處理刪除草稿對話框
  const openDeleteDialog = (id: number, type: "viewpoint" | "discussion") => {
    setDraftToDelete({ id, type })
    setDeleteDialogOpen(true)
  }

  // 處理確認刪除草稿
  const confirmDelete = () => {
    if (draftToDelete) {
      toast({
        title: "草稿已刪除",
        description: `${draftToDelete.type === "viewpoint" ? "觀點卡" : "討論"}草稿已成功刪除`,
      })
      // 實際應用中應該調用 API 刪除草稿
      setDeleteDialogOpen(false)
      setDraftToDelete(null)
    }
  }

  // 處理繼續編輯草稿
  const handleEditDraft = (id: number, type: "viewpoint" | "discussion") => {
    router.push(`/submit?draft=${id}&type=${type}`)
  }

  // 處理討論操作
  const handleDiscussionAction = (action: string, id: number) => {
    if (action === "edit") {
      router.push(`/submit?draft=${id}&type=discussion`)
    } else if (action === "view") {
      router.push(`/thread/${id}`)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">我的發表</h1>
        <div className="flex gap-2">
          <Button onClick={() => router.push("/submit")}>
            <Plus className="mr-2 h-4 w-4" />
            投稿觀點卡
          </Button>
          <Button onClick={() => router.push("/submit?type=discussion")}>
            <MessageSquare className="mr-2 h-4 w-4" />
            發起討論
          </Button>
        </div>
      </div>

      {/* 統計信息 - 移到標籤頁上方 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              觀點卡統計
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold">{publishedViewpoints.length}</div>
                <div className="text-xs text-muted-foreground">已發布</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">{pendingViewpoints.length}</div>
                <div className="text-xs text-muted-foreground">待審核</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">{draftViewpoints.length}</div>
                <div className="text-xs text-muted-foreground">草稿</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <MessageSquare className="h-5 w-5 mr-2" />
              討論統計
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="grid grid-cols-2 gap-2 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold">{publishedDiscussions.length}</div>
                <div className="text-xs text-muted-foreground">已發布</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">{draftDiscussions.length}</div>
                <div className="text-xs text-muted-foreground">草稿</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <CheckCircle className="h-5 w-5 mr-2" />
              互動統計
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {publishedViewpoints.reduce((sum, item) => sum + item.stats.likes, 0)}
                </div>
                <div className="text-xs text-muted-foreground">獲得點讚</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {publishedViewpoints.reduce((sum, item) => sum + item.stats.comments, 0)}
                </div>
                <div className="text-xs text-muted-foreground">獲得評論</div>
              </div>
              <div className="space-y-1">
                <div className="text-2xl font-bold">
                  {publishedViewpoints.reduce((sum, item) => sum + item.stats.bookmarks, 0)}
                </div>
                <div className="text-xs text-muted-foreground">被收藏</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Separator className="my-6" />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pending" className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            待審核
          </TabsTrigger>
          <TabsTrigger value="drafts" className="flex items-center">
            <FileEdit className="h-4 w-4 mr-2" />
            草稿
          </TabsTrigger>
          <TabsTrigger value="published" className="flex items-center">
            <CheckCircle className="h-4 w-4 mr-2" />
            已發布
          </TabsTrigger>
        </TabsList>

        {/* 待審核內容 */}
        <TabsContent value="pending" className="mt-6">
          {isLoadingData ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : pendingViewpoints.length > 0 ? (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold">待審核觀點卡</h2>
                <p className="text-sm text-muted-foreground">共 {pendingViewpoints.length} 張觀點卡</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {pendingViewpoints.map((viewpoint) => (
                  <div key={viewpoint.id} className="h-full">
                    <ContentCard {...viewpoint} />
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <Clock className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
              <h2 className="mt-4 text-xl font-medium">暫無待審核內容</h2>
              <p className="mt-2 text-muted-foreground">您目前沒有正在審核中的觀點卡或討論</p>
              <Button className="mt-4" asChild>
                <Link href="/submit">
                  <Plus className="mr-2 h-4 w-4" />
                  投稿觀點卡
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>

        {/* 草稿內容 */}
        <TabsContent value="drafts" className="mt-6">
          {isLoadingData ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : draftViewpoints.length > 0 || draftDiscussions.length > 0 ? (
            <div className="space-y-8">
              <Tabs value={activeSubTab} onValueChange={setActiveSubTab}>
                <TabsList>
                  <TabsTrigger value="viewpoints" className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    觀點卡草稿
                  </TabsTrigger>
                  <TabsTrigger value="discussions" className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    討論草稿
                  </TabsTrigger>
                </TabsList>

                {/* 觀點卡草稿 */}
                <TabsContent value="viewpoints" className="mt-4">
                  {draftViewpoints.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {draftViewpoints.map((draft) => (
                        <div key={draft.id} className="relative group h-full">
                          <ContentCard {...draft} />
                          <div className="absolute inset-0 bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <div className="flex gap-2">
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => openDeleteDialog(draft.id, "viewpoint")}
                              >
                                刪除
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleEditDraft(draft.id, "viewpoint")}
                              >
                                繼續編輯
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileEdit className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h3 className="mt-4 text-lg font-medium">暫無觀點卡草稿</h3>
                      <p className="mt-2 text-muted-foreground">您目前沒有保存的觀點卡草稿</p>
                    </div>
                  )}
                </TabsContent>

                {/* 討論草稿 - 使用精簡列表 */}
                <TabsContent value="discussions" className="mt-4">
                  {draftDiscussions.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {draftDiscussions.map((draft) => (
                        <div key={draft.id} className="relative group h-full">
                          <ContentCard {...draft} />
                          <div className="absolute inset-0 bg-black/5 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <div className="flex gap-2">
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => openDeleteDialog(draft.id, "discussion")}
                              >
                                刪除
                              </Button>
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleEditDraft(draft.id, "discussion")}
                              >
                                繼續編輯
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h3 className="mt-4 text-lg font-medium">暫無討論草稿</h3>
                      <p className="mt-2 text-muted-foreground">您目前沒有保存的討論草稿</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileEdit className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
              <h2 className="mt-4 text-xl font-medium">暫無草稿</h2>
              <p className="mt-2 text-muted-foreground">您目前沒有保存的草稿</p>
              <Button className="mt-4" asChild>
                <Link href="/submit">
                  <Plus className="mr-2 h-4 w-4" />
                  投稿觀點卡
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>

        {/* 已發布內容 */}
        <TabsContent value="published" className="mt-6">
          {isLoadingData ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : publishedViewpoints.length > 0 || publishedDiscussions.length > 0 ? (
            <div className="space-y-8">
              <Tabs value={activeSubTab} onValueChange={setActiveSubTab}>
                <TabsList>
                  <TabsTrigger value="viewpoints" className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    已發布觀點卡
                  </TabsTrigger>
                  <TabsTrigger value="discussions" className="flex items-center">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    已發布討論
                  </TabsTrigger>
                </TabsList>

                {/* 已發布觀點卡 */}
                <TabsContent value="viewpoints" className="mt-4">
                  {publishedViewpoints.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {publishedViewpoints.map((viewpoint) => (
                        <div key={viewpoint.id} className="h-full">
                          <ContentCard {...viewpoint} />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h3 className="mt-4 text-lg font-medium">暫無已發布觀點卡</h3>
                      <p className="mt-2 text-muted-foreground">您目前沒有已發布的觀點卡</p>
                    </div>
                  )}
                </TabsContent>

                {/* 已發布討論 - 使用精簡列表 */}
                <TabsContent value="discussions" className="mt-4">
                  {publishedDiscussions.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {publishedDiscussions.map((discussion) => (
                        <ContentCard key={discussion.id} {...discussion} onAction={handleDiscussionAction} />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
                      <h3 className="mt-4 text-lg font-medium">暫無已發布討論</h3>
                      <p className="mt-2 text-muted-foreground">您目前沒有已發布的討論</p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <div className="text-center py-12">
              <CheckCircle className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
              <h2 className="mt-4 text-xl font-medium">暫無已發布內容</h2>
              <p className="mt-2 text-muted-foreground">您目前沒有已發布的觀點卡或討論</p>
              <Button className="mt-4" asChild>
                <Link href="/submit">
                  <Plus className="mr-2 h-4 w-4" />
                  投稿觀點卡
                </Link>
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* 刪除確認對話框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-destructive mr-2" />
              確認刪除
            </DialogTitle>
            <DialogDescription>
              您確定要刪除這個{draftToDelete?.type === "viewpoint" ? "觀點卡" : "討論"}草稿嗎？此操作無法撤銷。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-2 sm:justify-end">
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              確認刪除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
