import { NextResponse } from "next/server"
import { getThreadViewCount } from "@/lib/thread-service"

export async function GET(request: Request, { params }: { params: { threadId: string } }) {
  try {
    const threadId = await params.threadId
    const response = await getThreadViewCount(threadId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching thread view count:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch view count" }, { status: 500 })
  }
}
