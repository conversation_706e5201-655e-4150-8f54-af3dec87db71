import { NextResponse } from "next/server"
import { getThreadCommentCount } from "@/lib/thread-service"

export async function GET(request: Request, { params }: { params: { threadId: string } }) {
  try {
    const threadId = await params.threadId
    const response = await getThreadCommentCount(threadId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching thread comment count:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch comment count" }, { status: 500 })
  }
}
