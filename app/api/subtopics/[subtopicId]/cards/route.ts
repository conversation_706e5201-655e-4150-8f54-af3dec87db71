import { NextResponse } from "next/server"
import { getCardsBySubtopic } from "@/lib/topic-card-service"

export async function GET(request: Request, { params }: { params: { subtopicId: string } }) {
  try {
    const subtopicId = await params.subtopicId
    const response = await getCardsBySubtopic(subtopicId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching cards by subtopic:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch cards" }, { status: 500 })
  }
}
