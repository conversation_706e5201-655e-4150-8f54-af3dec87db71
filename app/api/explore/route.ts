import { type NextRequest, NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(cookieStore)
    const searchParams = request.nextUrl.searchParams

    // Parse query parameters
    const type = searchParams.get("type") || "all"
    const sort = searchParams.get("sort") || "trending"
    const page = Number.parseInt(searchParams.get("page") || "1", 10)
    const limit = Number.parseInt(searchParams.get("limit") || "10", 10)
    const offset = (page - 1) * limit

    // Filter parameters
    const semanticTypes = searchParams.get("semanticTypes")?.split(",").filter(Boolean) || []
    const topics = searchParams.get("topics")?.split(",").filter(Boolean) || []
    const query = searchParams.get("q") || ""

    // Result containers
    let results = []
    let total = 0

    // Fetch data based on content type
    if (type === "all") {
      // For "all" type, fetch both cards and threads with pagination split between them
      const [cardsResult, threadsResult] = await Promise.all([
        fetchCards({
          supabase,
          semanticTypes,
          topics,
          query,
          sort,
          page,
          limit: Math.ceil(limit / 2), // Split the limit between cards and threads
        }),
        fetchThreads({
          supabase,
          semanticTypes,
          topics,
          query,
          sort,
          page,
          limit: Math.floor(limit / 2), // Split the limit between cards and threads
        }),
      ])

      results = [...cardsResult.data, ...threadsResult.data]
      total = cardsResult.total + threadsResult.total

      // Sort combined results based on the sort parameter
      if (sort === "latest") {
        results.sort((a, b) => new Date(b.rawTimestamp).getTime() - new Date(a.rawTimestamp).getTime())
      } else if (sort === "most_liked") {
        results.sort((a, b) => (b.stats?.likes || 0) - (a.stats?.likes || 0))
      }

      // Apply final pagination to combined results
      results = results.slice(0, limit)
    } else if (type === "viewpoint") {
      const cardsResult = await fetchCards({
        supabase,
        semanticTypes,
        topics,
        query,
        sort,
        page,
        limit,
      })
      results = cardsResult.data
      total = cardsResult.total
    } else {
      // type === "discussion"
      const threadsResult = await fetchThreads({
        supabase,
        semanticTypes,
        topics,
        query,
        sort,
        page,
        limit,
      })
      results = threadsResult.data
      total = threadsResult.total
    }

    return NextResponse.json({
      success: true,
      data: results,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error("Explore API error:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch explore data" }, { status: 500 })
  }
}

// Optimized function to fetch cards with all related data in fewer queries
async function fetchCards({ supabase, semanticTypes, topics, query, sort, page, limit }) {
  const offset = (page - 1) * limit

  // Build the base query
  let cardsQuery = supabase.from("cards").select(
    `
    id, 
    title, 
    content, 
    semantic_type, 
    contribution_type, 
    original_author, 
    original_url, 
    created_at,
    profiles:author_id (id, name, avatar)
    `,
    { count: "exact" },
  )

  // Apply filters
  if (semanticTypes.length > 0) {
    cardsQuery = cardsQuery.in("semantic_type", semanticTypes)
  }

  if (query) {
    cardsQuery = cardsQuery.or(`title.ilike.%${query}%,content.ilike.%${query}%`)
  }

  // Apply sorting
  if (sort === "latest") {
    cardsQuery = cardsQuery.order("created_at", { ascending: false })
  } else if (sort === "most_liked") {
    // For most_liked, we'll sort after fetching the data
    cardsQuery = cardsQuery.order("created_at", { ascending: false })
  } else {
    // Default trending sort
    cardsQuery = cardsQuery.order("created_at", { ascending: false })
  }

  // Apply pagination
  cardsQuery = cardsQuery.range(offset, offset + limit - 1)

  // Execute the query
  const { data: cards, error, count } = await cardsQuery

  if (error) throw error

  const totalCards = count || 0

  if (!cards || cards.length === 0) {
    return { data: [], total: totalCards }
  }

  // Get card IDs for related data queries
  const cardIds = cards.map((card) => card.id)

  // Fetch all related data in parallel with optimized queries
  const [topicsData, subtopicsData, reactionsData, commentsData] = await Promise.all([
    // Fetch topics with a single query
    supabase
      .from("card_topics")
      .select("card_id, topics:topic_id(id, name)")
      .in("card_id", cardIds),

    // Fetch subtopics with a single query
    supabase
      .from("card_subtopics")
      .select("card_id, subtopics:subtopic_id(id, name)")
      .in("card_id", cardIds),

    // Fetch all reactions with a single query
    supabase
      .from("reactions")
      .select("item_id, reaction_type, count")
      .eq("item_type", "card")
      .in("item_id", cardIds)
      .eq("reaction_type", "like"),

    // Fetch all comments counts with a single query
    supabase
      .from("comments")
      .select("root_item_id, count")
      .eq("root_item_type", "card")
      .in("root_item_id", cardIds),
  ])

  // Create efficient lookup maps
  const topicsMap = createLookupMap(topicsData.data || [], "card_id", "topics")
  const subtopicsMap = createLookupMap(subtopicsData.data || [], "card_id", "subtopics")

  // Create reaction counts map
  const reactionsMap = {}
  if (reactionsData.data) {
    reactionsData.data.forEach((reaction) => {
      if (!reactionsMap[reaction.item_id]) {
        reactionsMap[reaction.item_id] = { like: 0 }
      }
      reactionsMap[reaction.item_id].like += 1
    })
  }

  // Create comments counts map
  const commentsMap = {}
  if (commentsData.data) {
    commentsData.data.forEach((comment) => {
      if (!commentsMap[comment.root_item_id]) {
        commentsMap[comment.root_item_id] = 0
      }
      commentsMap[comment.root_item_id] += 1
    })
  }

  // Format the cards with all related data
  const formattedCards = cards.map((card) => {
    // 添加调试信息
    console.log("Card data:", {
      id: card.id,
      title: card.title,
      contentLength: card.content ? card.content.length : 0,
      created_at: card.created_at,
    })

    return {
      id: card.id,
      title: card.title,
      content: card.content,
      contentType: "viewpoint",
      semanticType: card.semantic_type,
      contribution_type: card.contribution_type,
      originalAuthor: card.original_author,
      originalSource: card.original_url,
      author: {
        id: card.profiles?.id,
        name: card.profiles?.name,
        avatar: card.profiles?.avatar,
      },
      timestamp: new Date(card.created_at).toLocaleDateString("zh-TW"),
      rawTimestamp: card.created_at,
      topics: (topicsMap[card.id] || []).map((t) => t.name),
      subtopics: (subtopicsMap[card.id] || []).map((s) => s.name),
      stats: {
        likes: reactionsMap[card.id]?.like || 0,
        comments: commentsMap[card.id] || 0,
      },
    }
  })

  // If sorting by likes, sort the formatted cards
  if (sort === "most_liked") {
    formattedCards.sort((a, b) => (b.stats?.likes || 0) - (a.stats?.likes || 0))
  }

  return { data: formattedCards, total: totalCards }
}

// 修改 fetchThreads 函数，确保正确获取作者信息
async function fetchThreads({ supabase, semanticTypes, topics, query, sort, page, limit }) {
  const offset = (page - 1) * limit

  console.log("Fetching threads with params:", { semanticTypes, topics, query, sort, page, limit, offset })

  // 检查总数以避免范围错误
  const { count: totalCount, error: countError } = await supabase
    .from("threads")
    .select("id", { count: "exact", head: true })

  if (countError) {
    console.error("Count query error:", countError)
    throw countError
  }

  console.log("Total threads count:", totalCount)

  // 如果没有数据，直接返回空结果
  if (totalCount === 0) {
    return { data: [], total: 0 }
  }

  // 确保偏移量不超过总数
  const safeOffset = Math.min(offset, Math.max(0, totalCount - 1))
  const safeLimit = Math.min(limit, totalCount - safeOffset)

  console.log("Safe pagination:", { safeOffset, safeLimit })

  // 构建基本查询 - 使用明确的字段列表
  let threadsQuery = supabase.from("threads").select(
    `
    id, 
    title, 
    content, 
    semantic_type, 
    status, 
    created_at,
    author_id,
    profiles:author_id (id, name, avatar)
    `,
    { count: "exact" },
  )

  // 应用过滤条件
  if (semanticTypes.length > 0) {
    threadsQuery = threadsQuery.in("semantic_type", semanticTypes)
  }

  if (query) {
    threadsQuery = threadsQuery.or(`title.ilike.%${query}%,content.ilike.%${query}%`)
  }

  // 应用排序
  if (sort === "latest") {
    threadsQuery = threadsQuery.order("created_at", { ascending: false })
  } else if (sort === "most_liked") {
    threadsQuery = threadsQuery.order("created_at", { ascending: false })
  } else {
    // 默认排序
    threadsQuery = threadsQuery.order("created_at", { ascending: false })
  }

  // 应用安全的分页
  threadsQuery = threadsQuery.range(safeOffset, safeOffset + safeLimit - 1)

  // 执行查询
  const { data: threads, error } = await threadsQuery

  if (error) {
    console.error("Threads query error:", error)
    throw error
  }

  console.log("Fetched threads:", threads ? threads.length : 0)

  if (!threads || threads.length === 0) {
    return { data: [], total: totalCount }
  }

  // 详细记录每个线程的数据
  threads.forEach((thread, index) => {
    console.log(`Thread ${index + 1}:`, {
      id: thread.id,
      title: thread.title,
      contentType: typeof thread.content,
      contentLength: thread.content ? thread.content.length : 0,
      contentSample: thread.content ? thread.content.substring(0, 50) : null,
      created_at: thread.created_at,
      author_id: thread.author_id,
      author: thread.profiles ? thread.profiles.name : null,
    })
  })

  // 获取线程ID列表
  const threadIds = threads.map((thread) => thread.id)

  // 并行获取所有相关数据
  const [topicsData, subtopicsData, reactionsData, repliesData] = await Promise.all([
    // 获取主题
    supabase
      .from("thread_topics")
      .select("thread_id, topics:topic_id(id, name)")
      .in("thread_id", threadIds),

    // 获取子主题
    supabase
      .from("thread_subtopics")
      .select("thread_id, subtopics:subtopic_id(id, name)")
      .in("thread_id", threadIds),

    // 获取点赞数
    supabase
      .from("reactions")
      .select("item_id, reaction_type, count")
      .eq("item_type", "thread")
      .in("item_id", threadIds)
      .eq("reaction_type", "like"),

    // 获取回复数
    supabase
      .from("comments")
      .select("root_item_id, count")
      .eq("root_item_type", "thread")
      .in("root_item_id", threadIds),
  ])

  // 创建查找映射
  const topicsMap = createLookupMap(topicsData.data || [], "thread_id", "topics")
  const subtopicsMap = createLookupMap(subtopicsData.data || [], "thread_id", "subtopics")

  // 创建反应计数映射
  const reactionsMap = {}
  if (reactionsData.data) {
    reactionsData.data.forEach((reaction) => {
      if (!reactionsMap[reaction.item_id]) {
        reactionsMap[reaction.item_id] = { like: 0 }
      }
      reactionsMap[reaction.item_id].like += 1
    })
  }

  // 创建回复计数映射
  const repliesMap = {}
  if (repliesData.data) {
    repliesData.data.forEach((reply) => {
      if (!repliesMap[reply.root_item_id]) {
        repliesMap[reply.root_item_id] = 0
      }
      repliesMap[reply.root_item_id] += 1
    })
  }

  // 格式化线程数据
  const formattedThreads = threads.map((thread) => {
    // 确保日期格式正确
    let formattedDate = "未知日期"
    let rawTimestamp = null

    try {
      if (thread.created_at) {
        const date = new Date(thread.created_at)
        if (!isNaN(date.getTime())) {
          formattedDate = date.toLocaleDateString("zh-TW")
          rawTimestamp = thread.created_at
        }
      }
    } catch (e) {
      console.error("Date formatting error:", e)
    }

    // 确保内容是字符串
    const safeContent = typeof thread.content === "string" ? thread.content : ""

    // 确保标题是字符串
    const safeTitle = typeof thread.title === "string" ? thread.title : "无标题"

    // 记录详细的内容信息
    console.log(`Formatting thread ${thread.id}:`, {
      contentType: typeof thread.content,
      contentLength: thread.content ? thread.content.length : 0,
      contentSample: thread.content ? thread.content.substring(0, 50) : null,
      isNull: thread.content === null,
      isUndefined: thread.content === undefined,
      isEmptyString: thread.content === "",
      safeContent: safeContent.substring(0, 50),
      profiles: thread.profiles,
    })

    return {
      id: thread.id,
      title: safeTitle,
      content: safeContent,
      contentType: "discussion",
      semanticType: thread.semantic_type || "discussion",
      status: thread.status || "published",
      author: {
        id: thread.profiles?.id || "unknown",
        name: thread.profiles?.name || "未知作者",
        avatar: thread.profiles?.avatar || null,
      },
      timestamp: formattedDate,
      rawTimestamp: rawTimestamp,
      topics: (topicsMap[thread.id] || []).map((t) => t.name),
      tags: (subtopicsMap[thread.id] || []).map((s) => s.name),
      stats: {
        likes: reactionsMap[thread.id]?.like || 0,
        replies: repliesMap[thread.id] || 0,
      },
    }
  })

  // 记录最终格式化的数据
  console.log("Formatted threads sample:", formattedThreads.slice(0, 2))

  // 如果按点赞排序，对格式化后的线程进行排序
  if (sort === "most_liked") {
    formattedThreads.sort((a, b) => (b.stats?.likes || 0) - (a.stats?.likes || 0))
  }

  return { data: formattedThreads, total: totalCount }
}

// Helper function to create lookup maps from related data
function createLookupMap(data, idField, valueField) {
  const map = {}
  data.forEach((item) => {
    const id = item[idField]
    if (!map[id]) {
      map[id] = []
    }
    if (item[valueField]) {
      map[id].push(item[valueField])
    }
  })
  return map
}
