import { NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function POST(request: Request) {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // 檢查用戶是否已登入
    const {
      data: { session },
    } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
    }

    // 解析請求體
    let { itemType, itemId, reactionType } = await request.json()

    // 確保 itemId 是有效的 UUID 格式
    try {
      // 如果 itemId 是數字，轉換為字符串
      if (typeof itemId === "number") {
        itemId = itemId.toString()
      }

      // 檢查是否為有效的 UUID 格式
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
      if (!uuidRegex.test(itemId)) {
        // 如果不是 UUID 格式，嘗試將其轉換為 UUID v4 格式
        // 這裡我們只是簡單地檢查，實際上應該使用更複雜的邏輯
        console.log("itemId 不是有效的 UUID 格式:", itemId)
      }
    } catch (error) {
      console.error("處理 itemId 時出錯:", error)
      return NextResponse.json({ success: false, error: "無效的 itemId 格式" }, { status: 400 })
    }

    // 驗證必要參數
    if (!itemType || !itemId || !reactionType) {
      return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
    }

    // 檢查用戶是否已經對該項目做出了相同類型的反應
    const { data: existingReaction, error: checkError } = await supabase
      .from("reactions")
      .select("id")
      .eq("profile_id", session.user.id)
      .eq("item_type", itemType)
      .eq("item_id", itemId)
      .eq("reaction_type", reactionType)
      .maybeSingle()

    if (checkError) {
      console.error("檢查反應時出錯:", checkError)
      return NextResponse.json({ success: false, error: "檢查反應時出錯" }, { status: 500 })
    }

    // 如果已經有反應，則刪除它（取消反應）
    if (existingReaction) {
      const { error: deleteError } = await supabase.from("reactions").delete().eq("id", existingReaction.id)

      if (deleteError) {
        console.error("刪除反應時出錯:", deleteError)
        return NextResponse.json({ success: false, error: "刪除反應時出錯" }, { status: 500 })
      }

      return NextResponse.json({ success: true, data: { action: "removed" } })
    }

    // 如果是互斥反應（如點讚和倒讚），則先刪除互斥的反應
    if (reactionType === "like" || reactionType === "dislike") {
      const oppositeType = reactionType === "like" ? "dislike" : "like"

      await supabase
        .from("reactions")
        .delete()
        .eq("profile_id", session.user.id)
        .eq("item_type", itemType)
        .eq("item_id", itemId)
        .eq("reaction_type", oppositeType)
    }

    // 添加新的反應
    const { error: insertError } = await supabase.from("reactions").insert({
      profile_id: session.user.id,
      item_type: itemType,
      item_id: itemId,
      reaction_type: reactionType,
    })

    if (insertError) {
      console.error("添加反應時出錯:", insertError)
      return NextResponse.json({ success: false, error: "添加反應時出錯" }, { status: 500 })
    }

    return NextResponse.json({ success: true, data: { action: "added" } })
  } catch (error) {
    console.error("處理反應時出錯:", error)
    return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
  }
}
