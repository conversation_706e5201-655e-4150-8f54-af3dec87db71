import { type NextRequest, NextResponse } from "next/server"
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs"
import { cookies } from "next/headers"

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const itemType = searchParams.get("itemType")
    const itemId = searchParams.get("itemId")
    const reactionType = searchParams.get("reactionType")

    console.log("Checking reaction:", { itemType, itemId, reactionType })

    if (!itemType || !itemId || !reactionType) {
      return NextResponse.json({ success: false, error: "缺少必要參數" }, { status: 400 })
    }

    const supabase = createRouteHandlerClient({ cookies })

    // 獲取當前用戶
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession()

    if (sessionError) {
      console.error("獲取會話時出錯:", sessionError)
      return NextResponse.json({ success: false, error: sessionError.message }, { status: 500 })
    }

    if (!session) {
      console.log("用戶未登入")
      return NextResponse.json({ success: true, data: { hasReacted: false } })
    }

    const userId = session.user.id

    // 檢查用戶是否已經對該項目進行了反應
    const { data, error } = await supabase
      .from("reactions")
      .select("id")
      .eq("item_type", itemType)
      .eq("item_id", itemId)
      .eq("reaction_type", reactionType)
      .eq("profile_id", userId)
      .maybeSingle()

    if (error) {
      console.error("檢查反應時出錯:", error)
      return NextResponse.json({ success: false, error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true, data: { hasReacted: !!data } })
  } catch (error) {
    console.error("處理檢查反應時出錯:", error)
    return NextResponse.json({ success: false, error: "內部伺服器錯誤" }, { status: 500 })
  }
}
