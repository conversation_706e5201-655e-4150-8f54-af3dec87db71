import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function POST(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { collectionId, itemId, itemType } = await request.json()

        console.log("POST /api/collections/items 收到請求:", { collectionId, itemId, itemType, userId: user.id })

        // 驗證必要參數
        if (!collectionId || !itemId || !itemType) {
            console.error("缺少必要參數:", { collectionId, itemId, itemType })
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：收藏牆ID、項目ID或項目類型"
            }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({
                success: false,
                error: "項目類型必須是 card 或 thread"
            }, { status: 400 })
        }

        // 檢查收藏牆是否存在且屬於當前用戶
        const { data: collection, error: collectionError } = await supabase
            .from("collections")
            .select("id, user_id, name")
            .eq("id", collectionId)
            .eq("user_id", user.id)
            .single()

        if (collectionError || !collection) {
            return NextResponse.json({
                success: false,
                error: "收藏牆不存在或無權限訪問"
            }, { status: 404 })
        }

        // 檢查項目是否已經在收藏牆中
        const { data: existingItem, error: checkError } = await supabase
            .from("collection_items")
            .select("id")
            .eq("collection_id", collectionId)
            .eq(itemType === "card" ? "card_id" : "thread_id", itemId)
            .maybeSingle()

        if (checkError) {
            console.error("檢查收藏項目時出錯:", checkError)
            return NextResponse.json({
                success: false,
                error: "檢查收藏項目時出錯"
            }, { status: 500 })
        }

        // 如果項目已存在，返回提示
        if (existingItem) {
            return NextResponse.json({
                success: false,
                error: "項目已經在收藏牆中",
                data: { action: "already_exists" }
            }, { status: 400 })
        }

        // 構建插入數據
        const insertData: any = {
            collection_id: collectionId,
        }

        if (itemType === "card") {
            insertData.card_id = itemId
        } else {
            insertData.thread_id = itemId
        }

        // 添加項目到收藏牆
        const { data: collectionItem, error: insertError } = await supabase
            .from("collection_items")
            .insert(insertData)
            .select()
            .single()

        if (insertError) {
            console.error("添加收藏項目時出錯:", insertError)
            return NextResponse.json({
                success: false,
                error: "添加收藏項目失敗"
            }, { status: 500 })
        }

        // 更新收藏牆的 updated_at 時間
        await supabase
            .from("collections")
            .update({ updated_at: new Date().toISOString() })
            .eq("id", collectionId)

        return NextResponse.json({
            success: true,
            data: { action: "added", collectionItem },
            message: `項目已添加到「${collection.name}」`
        })

    } catch (error) {
        console.error("處理添加收藏項目請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function DELETE(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        // 解析請求體
        const { collectionId, itemId, itemType } = await request.json()

        // 驗證必要參數
        if (!collectionId || !itemId || !itemType) {
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：收藏牆ID、項目ID或項目類型"
            }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({
                success: false,
                error: "項目類型必須是 card 或 thread"
            }, { status: 400 })
        }

        // 檢查收藏牆是否存在且屬於當前用戶
        const { data: collection, error: collectionError } = await supabase
            .from("collections")
            .select("id, user_id, name")
            .eq("id", collectionId)
            .eq("user_id", user.id)
            .single()

        if (collectionError || !collection) {
            return NextResponse.json({
                success: false,
                error: "收藏牆不存在或無權限訪問"
            }, { status: 404 })
        }

        // 構建查詢條件
        let query = supabase
            .from("collection_items")
            .delete()
            .eq("collection_id", collectionId)

        if (itemType === "card") {
            query = query.eq("card_id", itemId)
        } else {
            query = query.eq("thread_id", itemId)
        }

        // 從收藏牆中移除項目
        const { error: deleteError } = await query

        if (deleteError) {
            console.error("移除收藏項目時出錯:", deleteError)
            return NextResponse.json({
                success: false,
                error: "移除收藏項目失敗"
            }, { status: 500 })
        }

        // 更新收藏牆的 updated_at 時間
        await supabase
            .from("collections")
            .update({ updated_at: new Date().toISOString() })
            .eq("id", collectionId)

        return NextResponse.json({
            success: true,
            data: { action: "removed" },
            message: `項目已從「${collection.name}」移除`
        })

    } catch (error) {
        console.error("處理移除收藏項目請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}

export async function GET(request: Request) {
    try {
        const cookieStore = cookies()
        const supabase = await createServerClient(cookieStore)
        const { searchParams } = new URL(request.url)
        const itemId = searchParams.get('itemId')
        const itemType = searchParams.get('itemType')

        console.log("GET /api/collections/items 收到請求:", { itemId, itemType })

        // 檢查用戶是否已登入
        const {
            data: { user },
            error: userError,
        } = await supabase.auth.getUser()

        if (userError) {
            console.error("用戶認證錯誤:", userError)
            return NextResponse.json({ success: false, error: userError.message }, { status: 500 })
        }

        if (!user) {
            console.error("用戶未登入")
            return NextResponse.json({ success: false, error: "未登入" }, { status: 401 })
        }

        console.log("用戶已認證:", user.id)

        // 驗證必要參數
        if (!itemId || !itemType) {
            console.error("缺少必要參數:", { itemId, itemType })
            return NextResponse.json({
                success: false,
                error: "缺少必要參數：項目ID或項目類型"
            }, { status: 400 })
        }

        // 驗證 itemType
        if (itemType !== "card" && itemType !== "thread") {
            return NextResponse.json({
                success: false,
                error: "項目類型必須是 card 或 thread"
            }, { status: 400 })
        }

        // 構建查詢條件
        let query = supabase
            .from("collection_items")
            .select(`
                collection_id,
                collections!inner(
                    id,
                    name,
                    user_id
                )
            `)

        if (itemType === "card") {
            query = query.eq("card_id", itemId)
        } else {
            query = query.eq("thread_id", itemId)
        }

        // 只返回屬於當前用戶的收藏牆
        query = query.eq("collections.user_id", user.id)

        const { data: collectionItems, error: queryError } = await query

        if (queryError) {
            console.error("查詢收藏牆關聯時出錯:", queryError)
            return NextResponse.json({
                success: false,
                error: "查詢收藏牆關聯失敗"
            }, { status: 500 })
        }

        return NextResponse.json({
            success: true,
            data: collectionItems || []
        })

    } catch (error) {
        console.error("處理獲取收藏牆關聯請求時出錯:", error)
        return NextResponse.json({
            success: false,
            error: "內部伺服器錯誤"
        }, { status: 500 })
    }
}