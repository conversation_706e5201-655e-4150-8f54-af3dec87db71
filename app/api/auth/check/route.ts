import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET() {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(cookieStore)

    const { data, error } = await supabase.auth.getSession()

    if (error) {
      console.error("Error checking auth status:", error)
      return NextResponse.json(
        {
          authenticated: false,
          error: error.message,
        },
        { status: 500 },
      )
    }

    return NextResponse.json({
      authenticated: !!data.session,
      user: data.session
        ? {
            id: data.session.user.id,
            email: data.session.user.email,
          }
        : null,
    })
  } catch (error) {
    console.error("Unexpected error in auth check:", error)
    return NextResponse.json(
      {
        authenticated: false,
        error: "Unexpected error",
      },
      { status: 500 },
    )
  }
}
