import { NextResponse } from "next/server"
import { getCardsByTopic } from "@/lib/topic-card-service"

export async function GET(request: Request, { params }: { params: { topicId: string } }) {
  try {
    const topicId = await params.topicId
    const response = await getCardsByTopic(topicId)
    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching cards by topic:", error)
    return NextResponse.json({ success: false, data: null, error: "Failed to fetch cards" }, { status: 500 })
  }
}
