import { NextResponse } from "next/server"
import { createServerClient } from "@/lib/supabase/server"
import { cookies } from "next/headers"

export async function GET() {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(cookieStore)

    // 獲取熱門主題
    // 這裡使用一個簡單的查詢，實際應該基於卡片和討論的數量或熱度
    const { data: topics, error } = await supabase.from("TOPICS").select("id, name").limit(10)

    if (error) throw error

    // 為每個主題獲取相關內容數量
    const topicsWithCount = await Promise.all(
      topics.map(async (topic) => {
        // 獲取相關卡片數量
        const { count: cardCount } = await supabase
          .from("CARD_TOPICS")
          .select("*", { count: "exact" })
          .eq("topic_id", topic.id)

        // 獲取相關討論數量
        const { count: threadCount } = await supabase
          .from("THREAD_TOPICS")
          .select("*", { count: "exact" })
          .eq("topic_id", topic.id)

        return {
          id: topic.id,
          name: topic.name,
          count: (cardCount || 0) + (threadCount || 0),
        }
      }),
    )

    // 按內容數量排序
    topicsWithCount.sort((a, b) => b.count - a.count)

    return NextResponse.json({
      success: true,
      topics: topicsWithCount,
    })
  } catch (error) {
    console.error("Trending topics API error:", error)
    return NextResponse.json({ success: false, error: "Failed to fetch trending topics" }, { status: 500 })
  }
}
