"use client"

import type React from "react"
import { ThemeProvider } from "@/components/theme-provider"
import { SidebarProvider, useSidebar } from "@/components/ui/sidebar"
import { MainSidebar } from "@/components/main-sidebar"
import { AuthProvider } from "@/contexts/auth-context"
import { Toaster } from "@/components/ui/toaster"
import { memo } from "react"
import { EnvChecker } from "@/components/env-checker"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"

// 創建 React Query 客戶端
const queryClient = new QueryClient()

// 更新 LayoutContent 組件中的側欄寬度
const LayoutContent = memo(function LayoutContent({ children }: { children: React.ReactNode }) {
  const { collapsed } = useSidebar()

  return (
    <div className="flex min-h-screen bg-background">
      <MainSidebar />
      <main className={`flex-1 transition-all duration-300 ease-in-out ${collapsed ? "ml-12" : "ml-60"}`}>
        <div className="max-w-7xl mx-auto px-4 py-6">{children}</div>
      </main>
    </div>
  )
})

// 使用更新後的 ThemeProvider 設置
export default function ClientLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <SidebarProvider defaultCollapsed={false}>
            <LayoutContent>{children}</LayoutContent>
          </SidebarProvider>
          <Toaster />
          <EnvChecker />
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
}
