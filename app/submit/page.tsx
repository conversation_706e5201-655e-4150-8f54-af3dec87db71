import { Suspense } from "react"
import { SubmitTypeSelection } from "@/components/submit-type-selection"
import { SubmitForm } from "@/components/submit-form"
import { ClientSubmitPage } from "./client-page"

export default async function SubmitPage({
  searchParams,
}: {
  searchParams: { topic?: string; tag?: string; type?: string }
}) {
  return (
    <Suspense>
      <ClientSubmitPage searchParams={searchParams} />
    </Suspense>
  )
}
