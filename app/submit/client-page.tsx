"use client"

import { useState, useEffect } from "react"
import { SubmitTypeSelection } from "@/components/submit-type-selection"
import { SubmitForm } from "@/components/submit-form"
import { useRouter } from "next/navigation"

export function ClientSubmitPage({
    searchParams,
}: {
    searchParams: { topic?: string; tag?: string; type?: string }
}) {
    const [step, setStep] = useState<1 | 2>(1)
    const [contentType, setContentType] = useState<"original" | "others" | "discussion" | null>(
        searchParams.type === "original"
            ? "original"
            : searchParams.type === "others"
                ? "others"
                : searchParams.type === "discussion"
                    ? "discussion"
                    : null,
    )
    const router = useRouter()

    // If type is provided in URL params, go directly to step 2
    useEffect(() => {
        if (searchParams.type === "original" || searchParams.type === "others" || searchParams.type === "discussion") {
            setContentType(searchParams.type as "original" | "others" | "discussion")
            setStep(2)
        }
    }, [searchParams.type])

    const handleTypeSelect = (type: "original" | "others" | "discussion") => {
        setContentType(type)
        setStep(2)

        // Update URL with the selected type
        const params = new URLSearchParams(window.location.search)
        params.set("type", type)
        if (searchParams.topic) params.set("topic", searchParams.topic)
        if (searchParams.tag) params.set("tag", searchParams.tag)
        router.push(`/submit?${params.toString()}`)
    }

    const handleBackToSelection = () => {
        setStep(1)
        setContentType(null)

        // Update URL to remove type parameter
        const params = new URLSearchParams(window.location.search)
        params.delete("type")
        if (searchParams.topic) params.set("topic", searchParams.topic)
        if (searchParams.tag) params.set("tag", searchParams.tag)
        router.push(`/submit?${params.toString()}`)
    }

    return (
        <div className="container mx-auto py-6 space-y-6 max-w-7xl">
            <div className="space-y-2">
                <h1 className="text-3xl font-bold">{contentType === "discussion" ? "發起討論" : "投稿觀點卡"}</h1>
                <p className="text-muted-foreground">
                    {contentType === "discussion"
                        ? "提出問題或議題，邀請社群成員一起討論和交流想法"
                        : "分享你的 AI 技術觀點、實作經驗或踩坑記錄，幫助社群共同成長"}
                </p>
            </div>

            {step === 1 ? (
                <SubmitTypeSelection onSelect={handleTypeSelect} />
            ) : (
                <SubmitForm
                    defaultTopic={searchParams.topic}
                    defaultTag={searchParams.tag}
                    contentType={contentType || "original"}
                    onBack={handleBackToSelection}
                />
            )}
        </div>
    )
} 