"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/components/ui/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { Loader2 } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"

export default function LoginPage() {
  // 所有狀態和鉤子在組件頂部聲明
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [alreadyLoggedIn, setAlreadyLoggedIn] = useState(false)
  const { toast } = useToast()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { signIn, isAuthenticated, signOut, profile } = useAuth()
  const supabase = createClientComponentClient()

  // 初始檢查會話 - 修改為顯示已登入提示而非立即重定向
  useEffect(() => {
    const initialCheck = async () => {
      try {
        const { data } = await supabase.auth.getSession()
        if (data.session) {
          // 不立即重定向，而是顯示已登入狀態
          setAlreadyLoggedIn(true)
          setIsLoading(false)
        } else {
          setIsLoading(false)
        }
      } catch (error) {
        console.error("Initial session check error:", error)
        setIsLoading(false)
      }
    }

    initialCheck()
  }, [supabase.auth])

  // 監聽認證狀態變化 - 不再自動重定向
  useEffect(() => {
    if (isAuthenticated) {
      setAlreadyLoggedIn(true)
    }
  }, [isAuthenticated])

  // 處理登入
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email || !password) {
      toast({
        title: "請填寫所有必填欄位",
        description: "電子郵件和密碼為必填項",
        variant: "destructive",
      })
      return
    }

    try {
      setIsLoading(true)

      // 顯示登入中的提示
      const loadingToast = toast({
        title: "登入中...",
        description: "正在驗證您的憑證",
      })

      const { error } = await signIn(email, password)

      if (error) {
        throw error
      }

      // 登入成功
      toast({
        id: loadingToast.id,
        title: "登入成功",
        description: "歡迎回來！",
      })

      setIsRedirecting(true)
      const redirectTo = searchParams.get("redirectTo") || "/"
      router.push(redirectTo)
    } catch (error: any) {
      setIsLoading(false)
      toast({
        title: "登入失敗",
        description: error.message || "請檢查您的電子郵件和密碼",
        variant: "destructive",
      })
    }
  }

  // 處理繼續到首頁
  const handleContinue = () => {
    setIsRedirecting(true)
    const redirectTo = searchParams.get("redirectTo") || "/"
    router.push(redirectTo)
  }

  // 處理登出
  const handleLogout = async () => {
    setIsLoading(true)
    await signOut()
    setAlreadyLoggedIn(false)
    setIsLoading(false)
    toast({
      title: "已登出",
      description: "您已成功登出",
    })
  }

  // 渲染 UI
  return (
    <div className="flex justify-center items-center min-h-[80vh] px-4">
      {isLoading ? (
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin mb-2" />
          <span>載入中...</span>
        </div>
      ) : isRedirecting ? (
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin mb-2" />
          <span>重定向中...</span>
        </div>
      ) : alreadyLoggedIn ? (
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">您已登入</CardTitle>
            <CardDescription>您已經以 {profile?.name || profile?.email || "用戶"} 的身份登入</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-center text-muted-foreground">您可以繼續瀏覽或登出後使用其他帳號登入</p>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <Button onClick={handleContinue} className="w-full">
              繼續瀏覽
            </Button>
            <Button onClick={handleLogout} variant="outline" className="w-full">
              登出
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">登入</CardTitle>
            <CardDescription>輸入您的電子郵件和密碼登入</CardDescription>
          </CardHeader>
          <form onSubmit={handleLogin}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">電子郵件</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">密碼</Label>
                  <Link href="/auth/forgot-password" className="text-sm text-primary hover:underline">
                    忘記密碼?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    登入中...
                  </>
                ) : (
                  "登入"
                )}
              </Button>
              <div className="text-center text-sm">
                還沒有帳號?{" "}
                <Link href="/auth/register" className="text-primary hover:underline">
                  註冊
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      )}
    </div>
  )
}
