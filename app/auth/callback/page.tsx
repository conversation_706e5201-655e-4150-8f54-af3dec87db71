"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { supabase } from "@/lib/supabase/client"
import { Loader2 } from "lucide-react"

export default function AuthCallbackPage() {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error("Error during auth callback:", error)
          setError("身份驗證過程中發生錯誤，請重試")
          return
        }

        if (!data.session) {
          setError("無法獲取會話信息，請重新登入")
          return
        }

        // 成功獲取會話後重定向到首頁
        router.push("/")
      } catch (err) {
        console.error("Unexpected error during auth callback:", err)
        setError("發生意外錯誤，請重試")
      }
    }

    handleAuthCallback()
  }, [router])

  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh]">
      {error ? (
        <div className="text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button onClick={() => router.push("/auth/login")} className="px-4 py-2 bg-primary text-white rounded-md">
            返回登入頁面
          </button>
        </div>
      ) : (
        <>
          <Loader2 className="h-8 w-8 animate-spin mb-4" />
          <p className="text-center text-muted-foreground">正在處理登入，請稍候...</p>
        </>
      )}
    </div>
  )
}
