import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { ThreadDetailContent } from "@/components/thread-detail-content"
import { Suspense } from "react"
import { cookies } from "next/headers"
import { createServerClient } from "@/lib/supabase/server"

// 檢查字符串是否為有效的 UUID
function isValidUUID(uuid: string) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

interface ThreadPageProps {
  params: {
    id: string
  }
}

// 動態生成元數據
export async function generateMetadata({ params }: ThreadPageProps): Promise<Metadata> {
  try {
    const thread = await getThreadById(params.id)

    if (!thread) {
      return {
        title: "討論不存在",
        description: "找不到請求的討論",
      }
    }

    return {
      title: `${thread.title} | AILogora`,
      description: typeof thread.content === "string" ? thread.content.substring(0, 160) : "討論內容",
    }
  } catch (error) {
    console.error("Error generating metadata:", error)
    return {
      title: "載入中... | AILogora",
      description: "正在載入討論內容",
    }
  }
}

// 從 Supabase 獲取討論詳情
async function getThreadById(id: string) {
  try {
    const cookieStore = cookies()
    const supabase = createServerClient(cookieStore)

    // 1. 獲取討論基本信息 - 修正表名和關聯
    const { data: thread, error } = await supabase
      .from("threads")
      .select(`
        *,
        profiles!author_id(id,name,email,avatar,bio)
      `)
      .eq("id", id)
      .single()

    if (error) {
      console.error("Error fetching thread:", error)
      return null
    }

    if (!thread) {
      return null
    }

    // 2. 獲取討論的評論 - 修正表名和關聯
    const { data: comments, error: commentsError } = await supabase
      .from("comments")
      .select(`
        *,
        profiles!author_id(id,name,avatar,bio)
      `)
      .eq("root_item_id", id)
      .eq("root_item_type", "thread")
      .order("created_at", { ascending: true })

    if (commentsError) {
      console.error("Error fetching comments:", commentsError)
    }

    // 3. 獲取評論中引用的卡片
    const commentIds = comments ? comments.map((comment) => comment.id) : []
    const referencedCards = {}

    if (commentIds.length > 0) {
      const { data: references, error: referencesError } = await supabase
        .from("content_references")
        .select(`
          source_id,
          target_id,
          target_type,
          reference_type
        `)
        .eq("source_type", "comment")
        .in("source_id", commentIds)
        .eq("target_type", "card")

      if (referencesError) {
        console.error("Error fetching content references:", referencesError)
      } else if (references && references.length > 0) {
        // 獲取所有引用的卡片 ID
        const cardIds = references.map((ref) => ref.target_id)

        // 獲取引用的卡片詳情
        const { data: referencedCardsData, error: cardsError } = await supabase
          .from("cards")
          .select(`
            id,
            title,
            content,
            semantic_type,
            profiles!author_id(id,name)
          `)
          .in("id", cardIds)

        if (cardsError) {
          console.error("Error fetching referenced cards:", cardsError)
        } else if (referencedCardsData) {
          // 創建卡片 ID 到卡片詳情的映射
          referencedCardsData.forEach((card) => {
            referencedCards[card.id] = {
              id: card.id,
              title: card.title,
              content: card.content,
              type: card.semantic_type,
              author: card.profiles?.name || "未知作者",
              tags: [],
            }
          })

          // 將引用的卡片添加到對應的評論中
          if (comments) {
            comments.forEach((comment) => {
              const reference = references.find((ref) => ref.source_id === comment.id)
              if (reference && referencedCards[reference.target_id]) {
                comment.quotedCard = referencedCards[reference.target_id]
              }
            })
          }
        }
      }
    }

    // 4. 獲取討論的點讚數和倒讚數
    const { count: likesCount, error: likesError } = await supabase
      .from("reactions")
      .select("*", { count: "exact", head: true })
      .eq("item_id", id)
      .eq("item_type", "thread")
      .eq("reaction_type", "like")

    if (likesError) {
      console.error("Error fetching likes count:", likesError)
    }

    const { count: dislikesCount, error: dislikesError } = await supabase
      .from("reactions")
      .select("*", { count: "exact", head: true })
      .eq("item_id", id)
      .eq("item_type", "thread")
      .eq("reaction_type", "dislike")

    if (dislikesError) {
      console.error("Error fetching dislikes count:", dislikesError)
    }

    // 5. 獲取評論的點讚數和倒讚數
    if (comments && comments.length > 0) {
      for (const comment of comments) {
        // 獲取評論的點讚數
        const { count: commentLikesCount, error: commentLikesError } = await supabase
          .from("reactions")
          .select("*", { count: "exact", head: true })
          .eq("item_id", comment.id)
          .eq("item_type", "comment")
          .eq("reaction_type", "like")

        if (!commentLikesError) {
          comment.likes = commentLikesCount || 0
        }

        // 獲取評論的倒讚數
        const { count: commentDislikesCount, error: commentDislikesError } = await supabase
          .from("reactions")
          .select("*", { count: "exact", head: true })
          .eq("item_id", comment.id)
          .eq("item_type", "comment")
          .eq("reaction_type", "dislike")

        if (!commentDislikesError) {
          comment.dislikes = commentDislikesCount || 0
        }
      }
    }

    // 修正 author 屬性
    const threadWithAuthor = {
      ...thread,
      author: thread.profiles,
      replies: comments
        ? comments.map((comment) => ({
            ...comment,
            author: comment.profiles,
          }))
        : [],
      likes: likesCount || 0,
      dislikes: dislikesCount || 0,
    }

    // 返回完整的討論數據
    return threadWithAuthor
  } catch (error) {
    console.error("Error in getThreadById:", error)
    return null
  }
}

// 加載中組件
function ThreadLoading() {
  return (
    <div className="space-y-6">
      {/* 麵包屑導航 - 加載中狀態 */}
      <div className="flex items-center text-sm text-muted-foreground mb-4">
        <div className="h-4 w-20 bg-gray-200 rounded"></div>
        <div className="h-4 w-4 mx-2 bg-gray-200 rounded"></div>
        <div className="h-4 w-24 bg-gray-200 rounded"></div>
      </div>

      {/* 討論內容 - 加載中狀態 */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
        <div className="h-32 bg-gray-200 rounded mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-full mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
        <div className="h-10 bg-gray-200 rounded w-1/4"></div>
      </div>

      {/* 回覆區 - 加載中狀態 */}
      <div className="pt-4">
        <div className="flex justify-between items-center border-b pb-1 mb-4">
          <div className="h-6 bg-gray-200 rounded w-24"></div>
        </div>
        <div className="h-32 bg-gray-200 rounded mb-4"></div>
      </div>
    </div>
  )
}

// 錯誤組件
function ThreadError({ error }: { error: Error }) {
  return (
    <div className="space-y-6">
      <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
        <h2 className="text-lg font-semibold text-red-800 mb-2">載入討論時出現錯誤</h2>
        <p className="text-red-700">{error.message}</p>
      </div>
      <div className="mt-4">
        <a href="/" className="text-blue-600 hover:underline">
          返回首頁
        </a>
      </div>
    </div>
  )
}

export default async function ThreadPage({ params }: ThreadPageProps) {
  try {
    const thread = await getThreadById(params.id)

    if (!thread) {
      notFound()
    }

    // 檢查用戶認證狀態
    const cookieStore = cookies()
    const supabase = createServerClient(cookieStore)
    const { data } = await supabase.auth.getSession()
    const isAuthenticated = !!data.session

    return (
      <Suspense fallback={<ThreadLoading />}>
        <div className="space-y-6">
          {/* Add a key to force client-side re-render when thread changes */}
          <ThreadDetailContent key={`thread-${params.id}`} thread={thread} initialAuthState={isAuthenticated} />
        </div>
      </Suspense>
    )
  } catch (error) {
    console.error("Error loading thread:", error)
    return <ThreadError error={error instanceof Error ? error : new Error("未知錯誤")} />
  }
}
