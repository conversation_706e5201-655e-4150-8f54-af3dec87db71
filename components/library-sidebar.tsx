"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import {
  BookMarked,
  Clock,
  Star,
  Home,
  Settings,
  ChevronDown,
  ChevronRight,
  Plus,
  Check,
  ArrowLeft,
} from "lucide-react"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { useState, useRef, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"

interface Category {
  id: string
  name: string
  count: number
  collections?: Array<{
    id: string
    name: string
    count: number
  }>
}

interface LibraryTag {
  id: number
  name: string
  count: number
}

interface LibrarySidebarProps {
  categories: Category[]
  tags: LibraryTag[]
  collections?: Array<{
    id: string
    name: string
    itemCount?: number
    categoryId?: string
  }>
  activeCategory?: string
  activeCollection?: string
  activeTag?: string
  onCategoryClick: (category: string) => void
  onCollectionClick: (collection: string) => void
  onTagClick: (tag: string) => void
  onQuickLinkClick: (link: string) => void
  onCreateCollection: () => void
  onCreateCategory: () => void
  onAddToCollection: (itemId: string, itemType: string, collectionId: string) => void
  onMoveCollectionToCategory?: (collectionId: string, categoryId: string | null) => void
}

export function LibrarySidebar({
  categories,
  tags,
  collections,
  activeCategory,
  activeCollection,
  activeTag,
  onCategoryClick,
  onCollectionClick,
  onTagClick,
  onQuickLinkClick,
  onCreateCollection,
  onCreateCategory,
  onAddToCollection,
  onMoveCollectionToCategory,
}: LibrarySidebarProps) {
  const { toast } = useToast()
  const router = useRouter()
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({})
  const [dragOverCollection, setDragOverCollection] = useState<string | null>(null)
  const [dragOverCategory, setDragOverCategory] = useState<string | null>(null)
  const [isDraggingOver, setIsDraggingOver] = useState(false)
  const [isDraggingCollection, setIsDraggingCollection] = useState(false)
  const [dragGuideVisible, setDragGuideVisible] = useState(false)
  const [draggedItemCollections, setDraggedItemCollections] = useState<string[]>([])
  const [draggedCollectionCurrentCategory, setDraggedCollectionCurrentCategory] = useState<string | null>(null)
  const dropTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const successTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const [successfulDrop, setSuccessfulDrop] = useState<string | null>(null)
  const sidebarRef = useRef<HTMLDivElement>(null)

  // 監聽全局拖拽事件
  useEffect(() => {
    const handleDragEnter = (e: DragEvent) => {
      // 檢查拖拽類型
      if (e.dataTransfer?.types.includes("text/collection-drag")) {
        // 拖拽收藏牆：不展開分類，只允許放到分類標題上
        console.log("Collection drag detected")
        setIsDraggingCollection(true)
        setIsDraggingOver(false)
        setDragGuideVisible(true)

        // 延遲獲取拖拽的收藏牆信息，確保 DOM 已經更新
        setTimeout(() => {
          const draggingElement = document.querySelector('.dragging-collection')
          console.log("查找到的拖拽元素:", draggingElement)
          if (draggingElement) {
            const categoryId = draggingElement.getAttribute('data-category-id')
            console.log("從 DOM 讀取的原始 categoryId:", categoryId, "類型:", typeof categoryId)
            // 將 "null" 字符串轉換為 null
            const actualCategoryId = categoryId === "null" ? null : categoryId
            setDraggedCollectionCurrentCategory(actualCategoryId)
            console.log("轉換後的 actualCategoryId:", actualCategoryId, "類型:", typeof actualCategoryId)
            console.log("當前所有分類:", categories.map(c => ({ id: c.id, name: c.name })))
          } else {
            console.log("未找到 .dragging-collection 元素")
          }
        }, 10) // 短暫延遲，確保 DOM 更新完成
      } else if (e.dataTransfer?.types.includes("text/card-drag") || e.dataTransfer?.types.includes("application/json")) {
        // 拖拽卡片：展開所有分類，方便用戶看到所有收藏牆
        console.log("Card drag detected")
        setIsDraggingOver(true)
        setIsDraggingCollection(false)
        setDragGuideVisible(true)

        // 嘗試從 DOM 中獲取拖拽項目的收藏牆信息
        try {
          const draggingElement = document.querySelector('.dragging-item')
          if (draggingElement) {
            const collectionIdsStr = draggingElement.getAttribute('data-collection-ids')
            if (collectionIdsStr) {
              const collectionIds = JSON.parse(collectionIdsStr)
              setDraggedItemCollections(collectionIds)
              console.log("從 DOM 獲取拖拽項目的收藏牆:", collectionIds)
            } else {
              setDraggedItemCollections([])
            }
          } else {
            setDraggedItemCollections([])
          }
        } catch (error) {
          console.error("獲取拖拽項目收藏牆信息時出錯:", error)
          setDraggedItemCollections([])
        }

        const allCategories = [...categories.map(c => c.id), "uncategorized"]
        setExpandedCategories(
          allCategories.reduce((acc, categoryId) => ({ ...acc, [categoryId]: true }), {})
        )
      }
    }

    const handleDragLeave = (e: DragEvent) => {
      // 確保離開的是整個文檔，而不是內部元素
      if (e.target === document.documentElement) {
        setIsDraggingOver(false)
        setIsDraggingCollection(false)
        setDragGuideVisible(false)
        setDraggedItemCollections([])
        setDraggedCollectionCurrentCategory(null)
      }
    }

    const handleDragEnd = () => {
      setIsDraggingOver(false)
      setIsDraggingCollection(false)
      setDragGuideVisible(false)
      setDraggedItemCollections([])
      setDraggedCollectionCurrentCategory(null)
    }

    document.addEventListener("dragenter", handleDragEnter)
    document.addEventListener("dragleave", handleDragLeave)
    document.addEventListener("dragend", handleDragEnd)
    document.addEventListener("drop", handleDragEnd)

    return () => {
      document.removeEventListener("dragenter", handleDragEnter)
      document.removeEventListener("dragleave", handleDragLeave)
      document.removeEventListener("dragend", handleDragEnd)
      document.removeEventListener("drop", handleDragEnd)
    }
  }, [categories])

  const toggleCategory = (category: string, e: React.MouseEvent) => {
    e.stopPropagation() // 防止事件冒泡
    setExpandedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }))
  }

  // 處理拖拽進入側邊欄
  const handleDragEnterSidebar = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDraggingOver(true)
  }

  // 處理拖拽離開側邊欄
  const handleDragLeaveSidebar = (e: React.DragEvent) => {
    // 確保離開的是側邊欄本身，而不是內部元素
    if (e.currentTarget.contains(e.relatedTarget as Node)) {
      return
    }
    setIsDraggingOver(false)
  }

  // 處理拖拽進入分類
  const handleDragEnterCategory = (categoryId: string, e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // 檢查拖拽類型 - 只有收藏牆拖拽才響應
    if (e.dataTransfer.types.includes("text/collection-drag")) {
      console.log("Category drag enter for collection:", categoryId)
      setDragOverCategory(categoryId)
    }
  }

  // 處理拖拽離開分類
  const handleDragLeaveCategory = () => {
    setDragOverCategory(null)
  }

  // 處理放置到分類
  const handleDropOnCategory = (e: React.DragEvent, categoryId: string | null) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOverCategory(null)
    setIsDraggingCollection(false)
    setDragGuideVisible(false)

    try {
      // 嘗試從拖拽數據中獲取 JSON
      const jsonData = e.dataTransfer.getData("application/json")
      if (!jsonData) {
        console.error("No JSON data found in drag event")
        return
      }

      const data = JSON.parse(jsonData)
      const { collectionId, collectionName, type } = data

      if (type === "collection" && collectionId && onMoveCollectionToCategory) {
        onMoveCollectionToCategory(collectionId, categoryId)

        // 顯示成功提示
        const categoryName = categoryId
          ? categories.find(c => c.id === categoryId)?.name
          : "未分類"

        toast({
          title: "已移動收藏牆",
          description: `「${collectionName}」已移動到「${categoryName}」`,
        })
      }
    } catch (err) {
      console.error("Failed to parse drag data:", err)
      toast({
        title: "移動失敗",
        description: "無法處理拖拽數據",
        variant: "destructive",
      })
    }
  }

  // 處理拖拽進入收藏牆
  const handleDragEnterCollection = (collectionId: string, e: React.DragEvent) => {
    // 檢查拖拽類型 - 只有卡片拖拽才響應
    if (e.dataTransfer.types.includes("text/card-drag") ||
        (e.dataTransfer.types.includes("application/json") && !e.dataTransfer.types.includes("text/collection-drag"))) {
      console.log("Collection drag enter for card:", collectionId)
      setDragOverCollection(collectionId)

      // 清除之前的超時
      if (dropTimeoutRef.current) {
        clearTimeout(dropTimeoutRef.current)
      }
    }
  }

  // 處理拖拽進入側邊欄時獲取拖拽數據
  const handleDragOverSidebar = (e: React.DragEvent) => {
    e.preventDefault()

    // 優先從 DOM 獲取拖拽數據，因為 getData 在某些瀏覽器中不可靠
    try {
      // 處理卡片拖拽
      if (e.dataTransfer.types.includes("text/card-drag")) {
        const draggingElement = document.querySelector('.dragging-item')
        if (draggingElement) {
          const collectionIdsStr = draggingElement.getAttribute('data-collection-ids')
          if (collectionIdsStr) {
            const collectionIds = JSON.parse(collectionIdsStr)
            setDraggedItemCollections(collectionIds)
            console.log("從 DOM 更新拖拽項目的收藏牆:", collectionIds)
          }
        }
      }

      // 處理收藏牆拖拽
      if (e.dataTransfer.types.includes("text/collection-drag")) {
        const draggingElement = document.querySelector('.dragging-collection')
        if (draggingElement) {
          const categoryId = draggingElement.getAttribute('data-category-id')
          console.log("handleDragOverSidebar - 從 DOM 讀取的原始 categoryId:", categoryId)
          // 將 "null" 字符串轉換為 null
          const actualCategoryId = categoryId === "null" ? null : categoryId
          setDraggedCollectionCurrentCategory(actualCategoryId)
          console.log("handleDragOverSidebar - 轉換後的 actualCategoryId:", actualCategoryId)
        } else {
          console.log("handleDragOverSidebar - 未找到 .dragging-collection 元素")
        }
      }
    } catch (error) {
      console.error("從 DOM 獲取拖拽數據時出錯:", error)
    }

    // 備用方案：嘗試從 dataTransfer 獲取數據
    try {
      const jsonData = e.dataTransfer.getData("application/json")
      if (jsonData) {
        const data = JSON.parse(jsonData)
        console.log("從 dataTransfer 獲取拖拽數據:", data)

        // 處理卡片拖拽
        if (!e.dataTransfer.types.includes("text/collection-drag")) {
          if (data.collectionIds && Array.isArray(data.collectionIds) && draggedItemCollections.length === 0) {
            setDraggedItemCollections(data.collectionIds)
            console.log("從 dataTransfer 設置拖拽項目的收藏牆:", data.collectionIds)
          }
        }

        // 處理收藏牆拖拽
        if (e.dataTransfer.types.includes("text/collection-drag") && data.type === "collection" && !draggedCollectionCurrentCategory) {
          setDraggedCollectionCurrentCategory(data.currentCategoryId || null)
          console.log("從 dataTransfer 設置拖拽收藏牆的當前分類:", data.currentCategoryId)
        }
      }
    } catch (error) {
      console.error("從 dataTransfer 解析拖拽數據時出錯:", error)
    }
  }

  // 處理拖拽離開收藏牆
  const handleDragLeaveCollection = () => {
    // 設置延遲，避免在子元素之間移動時觸發
    dropTimeoutRef.current = setTimeout(() => {
      setDragOverCollection(null)
    }, 50)
  }

  // 處理放置到收藏牆
  const handleDropOnCollection = (e: React.DragEvent, collectionId: string) => {
    e.preventDefault()
    setDragOverCollection(null)
    setIsDraggingOver(false)
    setDragGuideVisible(false)

    console.log("handleDropOnCollection called with collectionId:", collectionId)

    try {
      // 嘗試從拖拽數據中獲取 JSON
      const jsonData = e.dataTransfer.getData("application/json")
      console.log("Drag data received:", jsonData)

      if (!jsonData) {
        console.error("No JSON data found in drag event")
        return
      }

      const data = JSON.parse(jsonData)
      console.log("Parsed drag data:", data)

      // 檢查是否是收藏牆拖拽，如果是則忽略
      if (e.dataTransfer.types.includes("text/collection-drag")) {
        console.log("Collection drag detected, ignoring drop on collection")
        return
      }

      const { itemId, itemType, itemTitle } = data

      if (itemId && itemType) {
        console.log("Calling onAddToCollection with:", { itemId, itemType, collectionId })
        // 調用回調函數
        onAddToCollection(itemId, itemType, collectionId)

        // 顯示成功動畫
        setSuccessfulDrop(collectionId)

        // 清除成功動畫
        if (successTimeoutRef.current) {
          clearTimeout(successTimeoutRef.current)
        }
        successTimeoutRef.current = setTimeout(() => {
          setSuccessfulDrop(null)
        }, 2000)

        // 顯示成功提示
        const collectionName = collections?.find((c) => c.id === collectionId)?.name

        toast({
          title: "已添加到收藏牆",
          description: `「${itemTitle}」已添加到「${collectionName}」`,
        })
      } else {
        console.error("Missing itemId or itemType in drag data:", data)
      }
    } catch (err) {
      console.error("Failed to parse drag data:", err)
      toast({
        title: "添加失敗",
        description: "無法處理拖拽數據",
        variant: "destructive",
      })
    }
  }

  // 處理收藏牆點擊
  const handleCollectionClick = (collectionId: string, collectionName: string) => {
    // 導航到收藏牆詳細頁面
    router.push(`/library/collection/${collectionId}`)

    // 同時調用原有的點擊回調
    onCollectionClick(collectionName)
  }

  return (
    <div
      ref={sidebarRef}
      className={cn(
        "w-64 border-r bg-background h-screen sticky top-0 z-30",
        isDraggingOver && "w-80 shadow-lg border-primary border-r-2",
      )}
      onDragEnter={handleDragEnterSidebar}
      onDragOver={handleDragOverSidebar}
      onDragLeave={handleDragLeaveSidebar}
    >
      {/* 拖拽引導提示 */}
      {dragGuideVisible && (
        <div className="absolute right-0 top-1/2 -translate-y-1/2 -translate-x-1/2 bg-primary text-primary-foreground p-2 rounded-full shadow-lg animate-pulse z-10">
          <ArrowLeft className="h-5 w-5" />
        </div>
      )}

      <div className="p-4">
        <Link href="/" className="flex items-center gap-2 mb-6">
          <Home className="h-5 w-5" />
          <span className="font-semibold">返回首頁</span>
        </Link>
        <h2 className="text-lg font-semibold mb-2">我的收藏庫</h2>
      </div>
      <ScrollArea className="flex-1 h-[calc(100vh-9rem)]">
        <div className="px-4 py-2">
          <h3 className="text-sm font-medium mb-2 text-muted-foreground">快速訪問</h3>
          <div className="space-y-1">
            <Button variant="ghost" size="sm" className="w-full justify-start" onClick={() => onQuickLinkClick("all")}>
              <BookMarked className="h-4 w-4 mr-2" />
              所有收藏
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start"
              onClick={() => onQuickLinkClick("recent")}
            >
              <Clock className="h-4 w-4 mr-2" />
              最近添加
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start"
              onClick={() => onQuickLinkClick("starred")}
            >
              <Star className="h-4 w-4 mr-2" />
              已標記
            </Button>
          </div>
        </div>

        <Separator className="my-4" />

        <div className="px-4 py-2">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium text-muted-foreground">收藏牆分類</h3>
            <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onCreateCategory}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>

          {/* 拖拽提示 - 參考 Notion 的設計 */}
          {isDraggingOver && (
            <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 p-3 rounded-lg mb-3 text-sm text-center animate-in fade-in-0 slide-in-from-top-2 duration-300 shadow-sm">
              <div className="flex items-center justify-center gap-2 text-blue-700 dark:text-blue-300">
                <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse" />
                拖拽卡片到收藏牆以添加
              </div>
              {draggedItemCollections.length > 0 && (
                <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                  綠色標記表示已收藏的收藏牆
                </div>
              )}
            </div>
          )}

          {/* 收藏牆拖拽提示 - 參考 Trello 的設計 */}
          {isDraggingCollection && (
            <div className="bg-purple-50 dark:bg-purple-950 border border-purple-200 dark:border-purple-800 p-3 rounded-lg mb-3 text-sm text-center animate-in fade-in-0 slide-in-from-top-2 duration-300 shadow-sm">
              <div className="flex items-center justify-center gap-2 text-purple-700 dark:text-purple-300">
                <div className="h-2 w-2 bg-purple-500 rounded-full animate-pulse" />
                拖拽收藏牆到分類以移動
              </div>
              <div className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                綠色標記表示當前所在分類
              </div>
            </div>
          )}

          {/* 顯示分類和收藏牆 */}
          <div className="space-y-1">
            {/* 顯示有分類的收藏牆 */}
            {categories && categories.length > 0 && categories.map((category) => {
              const categoryCollections = collections?.filter(collection => {
                return collection.categoryId === category.id
              }) || []

              // 顯示所有分類，不論是否有收藏牆
              return (
                <div key={category.id}>
                  <div
                    className={cn(
                      "flex items-center rounded-md transition-all",
                      isDraggingCollection && "hover:bg-blue-50 dark:hover:bg-blue-950",
                      dragOverCategory === category.id && "bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 dark:border-blue-700",
                      // 如果拖拽的收藏牆當前在這個分類中，顯示特殊樣式（參考 Trello 的做法）
                      isDraggingCollection && draggedCollectionCurrentCategory === category.id && "bg-green-50 dark:bg-green-950 border-2 border-green-400 dark:border-green-600 shadow-md ring-2 ring-green-200 dark:ring-green-800"
                    )}
                    onDragEnter={(e) => handleDragEnterCategory(category.id, e)}
                    onDragOver={(e) => e.preventDefault()}
                    onDragLeave={handleDragLeaveCategory}
                    onDrop={(e) => handleDropOnCategory(e, category.id)}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 p-0"
                      onClick={(e) => toggleCategory(category.id, e)}
                    >
                      {expandedCategories[category.id] ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      variant={activeCategory === category.id ? "secondary" : "ghost"}
                      size="sm"
                      className={cn(
                        "flex-1 justify-start text-sm ml-1",
                        activeCategory === category.id && "font-medium"
                      )}
                      onClick={() => onCategoryClick(category.id)}
                    >
                      {category.name}

                      {/* 當前位置指示器 */}
                      {isDraggingCollection && draggedCollectionCurrentCategory === category.id && (
                        <div className="flex items-center ml-2">
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                          <span className="text-xs text-green-600 dark:text-green-400 ml-1 font-medium">當前位置</span>
                        </div>
                      )}

                      {/* 調試信息 - 可以在開發時啟用 */}
                      {isDraggingCollection && process.env.NODE_ENV === 'development' && (
                        <div className="text-xs text-gray-500 ml-2">
                          {draggedCollectionCurrentCategory === category.id ? '✓' : '✗'}
                          (當前: {draggedCollectionCurrentCategory || 'null'})
                        </div>
                      )}

                      <span className="ml-auto text-xs text-muted-foreground">
                        {categoryCollections.length}
                      </span>
                    </Button>
                  </div>

                  {/* 顯示該分類下的收藏牆 - 拖拽收藏牆時不顯示 */}
                  {expandedCategories[category.id] && !isDraggingCollection && (
                    <div className="ml-6 mt-1 space-y-1">
                      {categoryCollections.length > 0 ? (
                        categoryCollections.map((collection) => (
                          <div
                            key={collection.id}
                            className={cn(
                              "relative rounded-md transition-all",
                              dragOverCollection === collection.id && "bg-primary/20 border-2 border-primary",
                              successfulDrop === collection.id && "bg-green-100 dark:bg-green-900/20",
                              isDraggingOver && "scale-105 transform-gpu",
                              // 如果當前拖拽的項目已經在這個收藏牆中，顯示特殊樣式（參考 Notion 的做法）
                              isDraggingOver && draggedItemCollections.includes(collection.id) && "bg-green-50 dark:bg-green-950 border-2 border-green-400 dark:border-green-600 shadow-lg shadow-green-200 dark:shadow-green-900/50 ring-2 ring-green-200 dark:ring-green-800",
                            )}
                            onDragEnter={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              handleDragEnterCollection(collection.id, e)
                            }}
                            onDragOver={(e) => e.preventDefault()}
                            onDragLeave={handleDragLeaveCollection}
                            onDrop={(e) => handleDropOnCollection(e, collection.id)}
                          >
                            <Button
                              variant={activeCollection === collection.name ? "secondary" : "ghost"}
                              size="sm"
                              className={cn(
                                "w-full justify-start text-sm pl-2",
                                activeCollection === collection.name && "font-medium",
                                dragOverCollection === collection.id && "bg-primary/10",
                                // 如果項目已在此收藏牆中，顯示不同樣式
                                isDraggingOver && draggedItemCollections.includes(collection.id) && "text-green-600 dark:text-green-400"
                              )}
                              onClick={() => handleCollectionClick(collection.id, collection.name)}
                            >
                              <div className={cn(
                                "h-1.5 w-1.5 rounded-full mr-2",
                                isDraggingOver && draggedItemCollections.includes(collection.id)
                                  ? "bg-green-500"
                                  : "bg-muted-foreground"
                              )} />
                              {collection.name}

                              {/* 已儲存指示器 - 參考 GitHub 和 Notion 的做法 */}
                              {isDraggingOver && draggedItemCollections.includes(collection.id) && (
                                <div className="flex items-center ml-1">
                                  <Check className="h-3 w-3 text-green-600 dark:text-green-400" />
                                  <span className="text-xs text-green-600 dark:text-green-400 ml-1 font-medium">已收藏</span>
                                </div>
                              )}

                              {collection.itemCount !== undefined && (
                                <span className="ml-auto text-xs text-muted-foreground">
                                  {collection.itemCount}
                                </span>
                              )}

                              {/* 成功添加動畫 */}
                              {successfulDrop === collection.id && (
                                <Check className="h-4 w-4 ml-2 text-green-500 animate-in fade-in-0 zoom-in-0 duration-300" />
                              )}
                            </Button>

                            {/* 拖拽提示 */}
                            {dragOverCollection === collection.id && (
                              <div className="absolute inset-0 border-2 border-primary rounded-md pointer-events-none animate-pulse" />
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="ml-2 text-xs text-muted-foreground py-1">
                          尚無收藏牆
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            })}

            {/* 顯示未分類的收藏牆 */}
            {(() => {
              const uncategorizedCollections = collections?.filter(collection => {
                return !collection.categoryId
              }) || []

              // 總是顯示未分類區域
              return (
                <div key="uncategorized-section">
                  <div
                    className={cn(
                      "flex items-center rounded-md transition-all",
                      isDraggingCollection && "hover:bg-blue-50 dark:hover:bg-blue-950",
                      dragOverCategory === "uncategorized" && "bg-blue-100 dark:bg-blue-900 border-2 border-blue-300 dark:border-blue-700",
                      // 如果拖拽的收藏牆當前在未分類中，顯示特殊樣式（參考 Trello 的做法）
                      isDraggingCollection && !draggedCollectionCurrentCategory && "bg-green-50 dark:bg-green-950 border-2 border-green-400 dark:border-green-600 shadow-md ring-2 ring-green-200 dark:ring-green-800"
                    )}
                    onDragEnter={(e) => handleDragEnterCategory("uncategorized", e)}
                    onDragOver={(e) => e.preventDefault()}
                    onDragLeave={handleDragLeaveCategory}
                    onDrop={(e) => handleDropOnCategory(e, null)}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 p-0"
                      onClick={(e) => toggleCategory("uncategorized", e)}
                    >
                      {expandedCategories["uncategorized"] ? (
                        <ChevronDown className="h-3 w-3" />
                      ) : (
                        <ChevronRight className="h-3 w-3" />
                      )}
                    </Button>
                    <Button
                      variant={activeCategory === "uncategorized" ? "secondary" : "ghost"}
                      size="sm"
                      className={cn(
                        "flex-1 justify-start text-sm ml-1",
                        activeCategory === "uncategorized" && "font-medium"
                      )}
                      onClick={() => onCategoryClick("uncategorized")}
                    >
                      未分類

                      {/* 當前位置指示器 */}
                      {isDraggingCollection && !draggedCollectionCurrentCategory && (
                        <div className="flex items-center ml-2">
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                          <span className="text-xs text-green-600 dark:text-green-400 ml-1 font-medium">當前位置</span>
                        </div>
                      )}

                      {/* 調試信息 - 可以在開發時啟用 */}
                      {isDraggingCollection && process.env.NODE_ENV === 'development' && (
                        <div className="text-xs text-gray-500 ml-2">
                          {!draggedCollectionCurrentCategory ? '✓' : '✗'}
                          (當前: {draggedCollectionCurrentCategory || 'null'})
                        </div>
                      )}

                      <span className="ml-auto text-xs text-muted-foreground">
                        {uncategorizedCollections.length}
                      </span>
                    </Button>
                  </div>

                  {/* 顯示未分類的收藏牆 - 拖拽收藏牆時不顯示 */}
                  {expandedCategories["uncategorized"] && !isDraggingCollection && (
                    <div className="ml-6 mt-1 space-y-1">
                      {uncategorizedCollections.length > 0 ? (
                        uncategorizedCollections.map((collection) => (
                          <div
                            key={collection.id}
                            className={cn(
                              "relative rounded-md transition-all",
                              dragOverCollection === collection.id && "bg-primary/20 border-2 border-primary",
                              successfulDrop === collection.id && "bg-green-100 dark:bg-green-900/20",
                              isDraggingOver && "scale-105 transform-gpu",
                              // 如果當前拖拽的項目已經在這個收藏牆中，顯示特殊樣式（參考 Notion 的做法）
                              isDraggingOver && draggedItemCollections.includes(collection.id) && "bg-green-50 dark:bg-green-950 border-2 border-green-400 dark:border-green-600 shadow-lg shadow-green-200 dark:shadow-green-900/50 ring-2 ring-green-200 dark:ring-green-800",
                            )}
                            onDragEnter={(e) => {
                              e.preventDefault()
                              e.stopPropagation()
                              handleDragEnterCollection(collection.id, e)
                            }}
                            onDragOver={(e) => e.preventDefault()}
                            onDragLeave={handleDragLeaveCollection}
                            onDrop={(e) => handleDropOnCollection(e, collection.id)}
                          >
                            <Button
                              variant={activeCollection === collection.name ? "secondary" : "ghost"}
                              size="sm"
                              className={cn(
                                "w-full justify-start text-sm pl-2",
                                activeCollection === collection.name && "font-medium",
                                dragOverCollection === collection.id && "bg-primary/10",
                                // 如果項目已在此收藏牆中，顯示不同樣式
                                isDraggingOver && draggedItemCollections.includes(collection.id) && "text-green-600 dark:text-green-400"
                              )}
                              onClick={() => handleCollectionClick(collection.id, collection.name)}
                            >
                              <div className={cn(
                                "h-1.5 w-1.5 rounded-full mr-2",
                                isDraggingOver && draggedItemCollections.includes(collection.id)
                                  ? "bg-green-500"
                                  : "bg-muted-foreground"
                              )} />
                              {collection.name}

                              {/* 已儲存指示器 - 參考 GitHub 和 Notion 的做法 */}
                              {isDraggingOver && draggedItemCollections.includes(collection.id) && (
                                <div className="flex items-center ml-1">
                                  <Check className="h-3 w-3 text-green-600 dark:text-green-400" />
                                  <span className="text-xs text-green-600 dark:text-green-400 ml-1 font-medium">已收藏</span>
                                </div>
                              )}

                              {collection.itemCount !== undefined && (
                                <span className="ml-auto text-xs text-muted-foreground">
                                  {collection.itemCount}
                                </span>
                              )}

                              {/* 成功添加動畫 */}
                              {successfulDrop === collection.id && (
                                <Check className="h-4 w-4 ml-2 text-green-500 animate-in fade-in-0 zoom-in-0 duration-300" />
                              )}
                            </Button>

                            {/* 拖拽提示 */}
                            {dragOverCollection === collection.id && (
                              <div className="absolute inset-0 border-2 border-primary rounded-md pointer-events-none animate-pulse" />
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="ml-2 text-xs text-muted-foreground py-1">
                          尚無收藏牆
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            })()}

            {/* 如果沒有任何收藏牆，顯示提示 */}
            {(!collections || collections.length === 0) && (
              <div className="text-xs text-muted-foreground text-center py-4">
                <div className="mb-2">還沒有收藏牆</div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onCreateCollection}
                  className="text-xs"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  創建收藏牆
                </Button>
              </div>
            )}
          </div>
        </div>

        <Separator className="my-4" />

        <div className="px-4 py-2">
          <h3 className="text-sm font-medium mb-2 text-muted-foreground">標籤</h3>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge
                key={tag.id}
                variant={activeTag === tag.name ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => onTagClick(tag.name)}
              >
                #{tag.name}
              </Badge>
            ))}
          </div>
        </div>
      </ScrollArea>
      <div className="p-4 border-t">
        <Button variant="outline" size="sm" className="w-full" asChild>
          <Link href="/settings/library" className="flex items-center justify-center">
            <Settings className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>收藏庫設置</span>
          </Link>
        </Button>
      </div>
    </div>
  )
}
