"use client"

import type React from "react"

import { useState, useEffect, useRef, type RefObject, useCallback } from "react"
import { Badge } from "@/components/ui/badge"
import {
  BookOpen,
  Wrench,
  AlertTriangle,
  FlaskConical,
  LightbulbIcon,
  HelpCircle,
  MessageSquare,
  FileText,
  Loader2,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"

// 定义内容类型
type ContentType = "viewpoint" | "discussion"

// 語義類型配置
const semanticTypeConfig: Record<
  string,
  {
    icon: React.ReactNode
    label: string
    description: string
    color: string
  }
> = {
  concept: {
    icon: <BookOpen className="h-3.5 w-3.5" />,
    label: "概念整理",
    description: "原理、理論、詞彙解釋、系統性知識輸出",
    color: "bg-green-50 text-green-600 dark:bg-green-950 dark:text-green-300",
  },
  implementation: {
    icon: <Wrench className="h-3.5 w-3.5" />,
    label: "實作",
    description: "流程教學、步驟說明、指令解說",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  warning: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    label: "踩坑警示",
    description: "問題踩雷分享、Debug 解法、環境配置爛點",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
  experience: {
    icon: <FlaskConical className="h-3.5 w-3.5" />,
    label: "實測經驗",
    description: "自己做過的實驗結果、性能比較、效果說明",
    color: "bg-purple-50 text-purple-600 dark:bg-purple-950 dark:text-purple-300",
  },
  insight: {
    icon: <LightbulbIcon className="h-3.5 w-3.5" />,
    label: "看法",
    description: "對該主題的觀點、價值判斷、立場",
    color: "bg-amber-50 text-amber-600 dark:bg-amber-950 dark:text-amber-300",
  },
  debate: {
    icon: <HelpCircle className="h-3.5 w-3.5" />,
    label: "爭議論點",
    description: "值不值得做？哪個方法比較爛？",
    color: "bg-orange-50 text-orange-600 dark:bg-orange-950 dark:text-orange-300",
  },
  guide: {
    icon: <Wrench className="h-3.5 w-3.5" />,
    label: "工具教學",
    description: "流程教學、步驟說明、指令解說",
    color: "bg-blue-50 text-blue-600 dark:bg-blue-950 dark:text-blue-300",
  },
  trap: {
    icon: <AlertTriangle className="h-3.5 w-3.5" />,
    label: "踩坑警示",
    description: "問題踩雷分享、Debug 解法、環境配置爛點",
    color: "bg-red-50 text-red-600 dark:bg-red-950 dark:text-red-300",
  },
}

// 卡片來源類型配置 - 與 ViewCard.tsx 保持一致
const sourceTypeConfig: Record<
  string,
  {
    label: string
    badge: string
    color: string
    show: boolean
  }
> = {
  top_author: {
    label: "意見領袖",
    badge: "Leader",
    color: "bg-gradient-to-r from-amber-500 to-orange-500 text-white",
    show: true,
  },
  community: {
    label: "社群貢獻",
    badge: "Community",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    show: true,
  },
  curated: {
    label: "系統整理",
    badge: "Curated",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    show: true,
  },
  original: {
    label: "原創內容",
    badge: "原創內容",
    color: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    show: true,
  },
  others: {
    label: "他人觀點",
    badge: "",
    color: "",
    show: false,
  },
  leader: {
    label: "意見領袖",
    badge: "Leader",
    color: "bg-gradient-to-r from-amber-500 to-orange-500 text-white",
    show: true,
  },
}

// 内容类型配置
const contentTypeConfig: Record<
  ContentType,
  {
    icon: React.ReactNode
    label: string
    color: string
  }
> = {
  viewpoint: {
    icon: <FileText className="h-3.5 w-3.5" />,
    label: "觀點卡",
    color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
  },
  discussion: {
    icon: <MessageSquare className="h-3.5 w-3.5" />,
    label: "討論串",
    color: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
  },
}

interface CardMentionProps {
  textareaRef: RefObject<HTMLTextAreaElement>
  onSelectCard: (card: any) => void
}

export function CardMention({ textareaRef, onSelectCard }: CardMentionProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedIndex, setSelectedIndex] = useState(-1) // 初始值設為 -1，表示沒有選中任何卡片
  const menuRef = useRef<HTMLDivElement>(null)
  const [hoveredIndex, setHoveredIndex] = useState(-1) // 新增懸停索引狀態
  const [activeFilter, setActiveFilter] = useState<ContentType | "all">("all") // 添加过滤状态
  const [isLoading, setIsLoading] = useState(false)
  const [cards, setCards] = useState<any[]>([])
  const [threads, setThreads] = useState<any[]>([])
  const supabase = createClientComponentClient()
  const inputTimerRef = useRef<NodeJS.Timeout | null>(null)
  const filterClickedRef = useRef(false) // 新增參考來追蹤過濾器點擊

  // 處理卡片選擇
  const handleSelectCard = useCallback(
    (card: any) => {
      onSelectCard(card)
      setIsOpen(false)

      // 清除 @ 及後面的搜索詞
      if (textareaRef.current) {
        const text = textareaRef.current.value
        const cursorPosition = textareaRef.current.selectionStart
        const textBeforeCursor = text.substring(0, cursorPosition)
        const atIndex = textBeforeCursor.lastIndexOf("@")

        if (atIndex !== -1) {
          const newText = text.substring(0, atIndex) + text.substring(cursorPosition)
          textareaRef.current.value = newText
          textareaRef.current.selectionStart = atIndex
          textareaRef.current.selectionEnd = atIndex

          // 觸發 input 事件以更新 React 狀態
          const event = new Event("input", { bubbles: true })
          textareaRef.current.dispatchEvent(event)
        }
      }
    },
    [onSelectCard, textareaRef],
  )

  // 獲取卡片和討論串
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        // 獲取卡片
        const { data: cardsData, error: cardsError } = await supabase
          .from("cards")
          .select(`
            id, 
            title, 
            content, 
            semantic_type, 
            created_at, 
            contribution_type,
            topics (id, name),
            subtopics (id, name),
            profiles (id, name, avatar)
          `)
          .order("created_at", { ascending: false })
          .limit(20)

        if (cardsError) {
          console.error("Error fetching cards:", cardsError)
        } else {
          // 處理卡片數據
          const formattedCards = cardsData.map((card) => ({
            id: card.id,
            type: card.semantic_type || "concept",
            title: card.title || "",
            content: card.content || "",
            tags: card.subtopics ? card.subtopics.map((s: any) => s.name || "") : [],
            author: card.profiles ? card.profiles.name || "未知作者" : "未知作者",
            isLeader: card.contribution_type === "top_author",
            contentType: "viewpoint" as ContentType,
            topics: card.topics || [],
          }))
          setCards(formattedCards)
        }

        // 獲取討論串 - 使用正確的表名 threads
        const { data: threadsData, error: threadsError } = await supabase
          .from("threads")
          .select(`
            id, 
            title, 
            content, 
            semantic_type, 
            created_at,
            topics (id, name),
            subtopics (id, name),
            profiles (id, name, avatar)
          `)
          .order("created_at", { ascending: false })
          .limit(10)

        if (threadsError) {
          console.error("Error fetching threads:", threadsError)
        } else {
          // 處理討論串數據
          const formattedThreads = threadsData.map((thread) => ({
            id: thread.id,
            semantic_type: thread.semantic_type || "discussion",
            title: thread.title || "",
            content: thread.content || "",
            tags: thread.subtopics ? thread.subtopics.map((s: any) => s.name || "") : [],
            author: thread.profiles ? thread.profiles.name || "未知作者" : "未知作者",
            contentType: "discussion" as ContentType,
            topics: thread.topics || [],
          }))
          setThreads(formattedThreads)
        }
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    if (isOpen) {
      fetchData()
    }
  }, [isOpen, supabase])

  // 過濾卡片
  const filteredCards = [...cards, ...threads].filter((card) => {
    // 首先按內容類型過濾 - 這應該始終優先
    if (activeFilter !== "all" && card.contentType !== activeFilter) {
      return false
    }

    const term = searchTerm.toLowerCase()

    // 只有當 activeFilter 為 "all" 時才檢查特殊前綴
    if (activeFilter === "all") {
      // 檢查特殊前綴
      if (term === "card" || term.startsWith("card:")) {
        return card.contentType === "viewpoint"
      }

      if (term === "thread" || term.startsWith("thread:")) {
        return card.contentType === "discussion"
      }
    }

    // 如果有搜索詞，按它過濾
    if (term) {
      return (
        (card.title && card.title.toLowerCase().includes(term)) ||
        (card.content && card.content.toLowerCase().includes(term)) ||
        (card.tags && card.tags.some((tag) => tag && tag.toLowerCase().includes(term))) ||
        (card.author && typeof card.author === "string" && card.author.toLowerCase().includes(term)) ||
        (card.type && card.type.toLowerCase().includes(term))
      )
    }

    // 如果沒有搜索詞，顯示所有卡片
    return true
  })

  // 按内容类型分组
  const viewpointCards = filteredCards.filter((card) => card.contentType === "viewpoint")
  const discussionCards = filteredCards.filter((card) => card.contentType === "discussion")

  // 监听文本框输入
  useEffect(() => {
    const textarea = textareaRef.current
    if (!textarea) return

    // 直接處理輸入，不使用防抖
    const handleInput = () => {
      if (!textarea) return

      // 如果是過濾器點擊，跳過輸入處理
      if (filterClickedRef.current) {
        filterClickedRef.current = false
        return
      }

      const text = textarea.value
      const cursorPosition = textarea.selectionStart || 0

      // 檢查光標前的文本是否包含 @
      const textBeforeCursor = text.substring(0, cursorPosition)
      const atIndex = textBeforeCursor.lastIndexOf("@")

      if (atIndex !== -1 && (atIndex === 0 || /\s/.test(textBeforeCursor[atIndex - 1]))) {
        // 提取 @ 後面的搜索詞
        const searchText = textBeforeCursor.substring(atIndex + 1)

        // 如果有 @ 且後面沒有空格，則打開菜單
        if (!searchText.includes(" ")) {
          setSearchTerm(searchText)
          setIsOpen(true)
          setSelectedIndex(-1) // 重置選中索引
          setHoveredIndex(-1) // 重置懸停索引

          // 檢查是否有高級過濾前綴
          if (searchText === "card" || searchText.startsWith("card:")) {
            setActiveFilter("viewpoint")
          } else if (searchText === "thread" || searchText.startsWith("thread:")) {
            setActiveFilter("discussion")
          } else {
            // 只有在沒有特殊前綴時才重置過濾器
            if (!filterClickedRef.current) {
              setActiveFilter("all")
            }
          }
        } else {
          setIsOpen(false)
        }
      } else {
        setIsOpen(false)
      }
    }

    const handleKeyDown = (e: KeyboardEvent) => {
      // 如果輸入了 @ 符號，立即打開菜單
      if (e.key === "@") {
        // 延遲一點點執行，確保 @ 已經輸入到文本框中
        setTimeout(() => {
          setIsOpen(true)
          setSearchTerm("")
          // 只有在沒有特殊前綴時才重置過濾器
          if (!filterClickedRef.current) {
            setActiveFilter("all")
          }
        }, 10)
      }

      // 只有當菜單打開時才處理特殊按鍵
      if (!isOpen) return

      // 計算總條目數
      const totalItems = filteredCards.length

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault()
          setSelectedIndex((prev) => (prev < totalItems - 1 ? prev + 1 : prev))
          break
        case "ArrowUp":
          e.preventDefault()
          setSelectedIndex((prev) => (prev > 0 ? prev - 1 : 0))
          break
        case "Enter":
        case "Tab":
          if (totalItems > 0 && selectedIndex >= 0) {
            e.preventDefault()
            handleSelectCard(filteredCards[selectedIndex])
          } else if (totalItems > 0) {
            // 如果沒有選中項但有過濾結果，選擇第一個
            e.preventDefault()
            handleSelectCard(filteredCards[0])
          }
          break
        case "Escape":
          e.preventDefault()
          setIsOpen(false)
          break
      }
    }

    // 添加事件監聽器
    textarea.addEventListener("input", handleInput)
    textarea.addEventListener("keydown", handleKeyDown)

    // 初始檢查，以防文本框已經有 @ 符號
    handleInput()

    return () => {
      textarea.removeEventListener("input", handleInput)
      textarea.removeEventListener("keydown", handleKeyDown)
      if (inputTimerRef.current) clearTimeout(inputTimerRef.current)
    }
  }, [textareaRef, isOpen, selectedIndex, filteredCards, handleSelectCard])

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        textareaRef.current &&
        !textareaRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [textareaRef])

  // 截断内容
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return ""
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + "..."
  }

  // 處理過濾器點擊
  const handleFilterClick = (filter: ContentType | "all") => {
    // 阻止事件傳播
    filterClickedRef.current = true

    // 設置過濾器狀態
    setActiveFilter(filter)
    setSelectedIndex(-1)
    setHoveredIndex(-1)

    // 如果搜索詞是特殊前綴，則清除它
    if (
      searchTerm === "card" ||
      searchTerm.startsWith("card:") ||
      searchTerm === "thread" ||
      searchTerm.startsWith("thread:")
    ) {
      setSearchTerm("")
    }
  }

  if (!isOpen) return null

  // 渲染卡片项
  const renderCardItem = (card: any, index: number, globalIndex: number) => {
    const typeConfig = semanticTypeConfig[card.type] || semanticTypeConfig.concept
    const sourceConfig = sourceTypeConfig[card.isLeader ? "leader" : "community"]
    const contentConfig = contentTypeConfig[card.contentType]

    return (
      <div
        key={card.id}
        className={cn(
          "px-3 py-2 cursor-pointer hover:bg-muted border-b border-border/40 last:border-b-0",
          (globalIndex === selectedIndex || globalIndex === hoveredIndex) && "bg-muted",
        )}
        onClick={() => handleSelectCard(card)}
        onMouseEnter={() => setHoveredIndex(globalIndex)}
        onMouseLeave={() => setHoveredIndex(-1)}
      >
        <div className="flex flex-col gap-1">
          {/* 卡片头部 - 类型徽章和标签 */}
          <div className="flex flex-wrap items-center gap-2">
            {/* 内容类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", contentConfig.color)}>
              {contentConfig.icon}
              <span>{contentConfig.label}</span>
            </Badge>

            {/* 语义类型徽章 */}
            <Badge variant="outline" className={cn("flex items-center gap-1", typeConfig.color)}>
              {typeConfig.icon}
              <span>{typeConfig.label}</span>
            </Badge>

            {/* 主题徽章 */}
            {card.topics && card.topics.length > 0 && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <span>{card.topics[0].name}</span>
              </Badge>
            )}

            {/* 来源类型徽章 */}
            {card.isLeader && sourceConfig.show && (
              <Badge variant="default" className={cn("flex items-center gap-1", sourceConfig.color)}>
                <span>{sourceConfig.badge}</span>
              </Badge>
            )}

            {/* 热门标签 */}
            {card.isHot && (
              <Badge variant="destructive" className="text-xs py-0">
                熱門
              </Badge>
            )}
          </div>

          {/* 作者信息 */}
          <div className="text-xs text-muted-foreground">作者：{card.author}</div>

          {/* 标题 */}
          <h4 className="text-sm font-bold text-foreground">{card.title}</h4>

          {/* 内容 */}
          <div className="text-xs text-muted-foreground">{truncateText(card.content, 80)}</div>

          {/* 标签 */}
          <div className="flex flex-wrap gap-1 mt-1">
            {card.tags &&
              card.tags.map((tag: string, tagIndex: number) => (
                <Badge key={tagIndex} variant="outline" className="text-xs py-0">
                  #{tag}
                </Badge>
              ))}
          </div>

          {/* 讨论串特有信息 */}
          {card.contentType === "discussion" && (
            <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3.5 w-3.5" />
                <span>{card.replies || 0}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div
      ref={menuRef}
      className="absolute z-50 w-96 max-h-80 overflow-y-auto bg-background border rounded-md shadow-lg"
      style={{
        top: "100%", // 显示在文本框下方
        left: 0,
        marginTop: "5px",
      }}
    >
      {/* 过滤器选项 */}
      <div className="sticky top-0 z-10 bg-background border-b flex items-center p-2 gap-1 flex-nowrap">
        <Badge
          variant={activeFilter === "all" ? "default" : "outline"}
          className="cursor-pointer flex items-center gap-1 whitespace-nowrap"
          onClick={(e) => {
            e.stopPropagation() // 阻止事件傳播
            handleFilterClick("all")
          }}
        >
          全部
        </Badge>
        <Badge
          variant={activeFilter === "viewpoint" ? "default" : "outline"}
          className={cn(
            "cursor-pointer flex items-center gap-1 whitespace-nowrap",
            activeFilter === "viewpoint" ? "bg-blue-600" : "",
          )}
          onClick={(e) => {
            e.stopPropagation() // 阻止事件傳播
            handleFilterClick("viewpoint")
          }}
        >
          {contentTypeConfig.viewpoint.icon}
          觀點卡
        </Badge>
        <Badge
          variant={activeFilter === "discussion" ? "default" : "outline"}
          className={cn(
            "cursor-pointer flex items-center gap-1 whitespace-nowrap",
            activeFilter === "discussion" ? "bg-purple-600" : "",
          )}
          onClick={(e) => {
            e.stopPropagation() // 阻止事件傳播
            handleFilterClick("discussion")
          }}
        >
          {contentTypeConfig.discussion.icon}
          討論串
        </Badge>

        {/* 提示文本 */}
        <div className="ml-auto text-xs text-muted-foreground pr-1 shrink-0">提示: @card @thread</div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          <span className="ml-2 text-sm text-muted-foreground">載入中...</span>
        </div>
      ) : filteredCards.length > 0 ? (
        <div className="py-1">
          {/* 如果有观点卡且不是只显示讨论串 */}
          {viewpointCards.length > 0 && activeFilter !== "discussion" && (
            <>
              {/* 只有当同时显示两种类型时才显示分组标题 */}
              {activeFilter === "all" && <div className="px-3 py-1 bg-muted/50 font-medium text-sm">觀點卡</div>}
              {viewpointCards.map((card, index) => renderCardItem(card, index, index))}
            </>
          )}

          {/* 如果有讨论串且不是只显示观点卡 */}
          {discussionCards.length > 0 && activeFilter !== "viewpoint" && (
            <>
              {/* 只有当同时显示两种类型且有观点卡时才添加分隔 */}
              {activeFilter === "all" && viewpointCards.length > 0 && (
                <div className="px-3 py-1 bg-muted/50 font-medium text-sm">討論串</div>
              )}
              {discussionCards.map((card, index) => renderCardItem(card, index, viewpointCards.length + index))}
            </>
          )}
        </div>
      ) : (
        <div className="px-3 py-2 text-sm text-muted-foreground">沒有找到相關內容</div>
      )}

      {/* 底部提示 */}
      <div className="px-3 py-2 text-xs text-muted-foreground border-t">按 ↑↓ 鍵選擇，Enter 確認</div>
    </div>
  )
}
