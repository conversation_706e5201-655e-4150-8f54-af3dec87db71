"use client"

import { useEffect, useState } from "react"
import { ContentCard } from "@/components/content-card"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { MessageSquare, Eye } from "lucide-react"
import Link from "next/link"
import { Skeleton } from "@/components/ui/skeleton"

interface TopicDataLoaderProps {
  topicId: string
  activeSubtopic: string | null
  activeSubtopicId: string | null
  activeTab: string
}

export function TopicDataLoader({ topicId, activeSubtopic, activeSubtopicId, activeTab }: TopicDataLoaderProps) {
  const [cards, setCards] = useState<any[]>([])
  const [threads, setThreads] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function loadData() {
      setLoading(true)

      if (activeTab === "viewpoints") {
        // 加載卡片
        let cardsResponse
        if (activeSubtopicId) {
          const res = await fetch(`/api/subtopics/${activeSubtopicId}/cards`)
          cardsResponse = await res.json()
        } else {
          const res = await fetch(`/api/topics/${topicId}/cards`)
          cardsResponse = await res.json()
        }

        if (cardsResponse.success && cardsResponse.data) {
          // 獲取每張卡片的統計數據
          const cardsWithStats = await Promise.all(
            cardsResponse.data.map(async (card: any) => {
              const statsRes = await fetch(`/api/cards/${card.id}/stats`)
              const statsResponse = await statsRes.json()

              // 從資料庫獲取作者資訊
              const author = {
                id: card.author_id || "",
                name: card.author?.name || "匿名用戶",
                avatar: card.author?.avatar || "",
              }

              // 處理主題和子主題數據
              const topicNames = card.topics ? card.topics.map((t: any) => t.name) : []
              const subtopicNames = card.subtopics ? card.subtopics.map((s: any) => s.name) : []

              return {
                id: card.id,
                title: card.title,
                content: card.content,
                author: author,
                stats: statsResponse.success
                  ? statsResponse.data
                  : { likes: 0, dislikes: 0, comments: 0, bookmarks: 0 },
                contentType: "viewpoint",
                semanticType: card.semantic_type || "concept",
                variant: "grid",
                topics: topicNames,
                subtopics: subtopicNames,
                sourceType: card.contribution_type || "original",
                contribution_type: card.contribution_type || "community",
                originalAuthor: card.original_author || "",
                originalSource: card.original_url || "",
                timestamp: formatTimeAgo(card.created_at),
                features: { truncate: true },
              }
            }),
          )
          setCards(cardsWithStats)
        }
      } else if (activeTab === "discussions") {
        // 加載討論串
        const res = await fetch(`/api/topics/${topicId}/threads`)
        const threadsResponse = await res.json()

        if (threadsResponse.success && threadsResponse.data) {
          // 獲取每個討論串的評論數和瀏覽數
          const threadsWithStats = await Promise.all(
            threadsResponse.data.map(async (thread: any) => {
              const commentsRes = await fetch(`/api/threads/${thread.id}/comments/count`)
              const commentsResponse = await commentsRes.json()

              const viewsRes = await fetch(`/api/threads/${thread.id}/views/count`)
              const viewsResponse = await viewsRes.json()

              return {
                id: thread.id,
                contentType: "discussion",
                variant: "default",
                title: thread.title,
                author: thread.author?.name || "匿名",
                replies: commentsResponse.success ? commentsResponse.data : 0,
                views: viewsResponse.success ? viewsResponse.data : 0,
                lastActive: formatTimeAgo(thread.created_at),
              }
            }),
          )
          setThreads(threadsWithStats)
        }
      }

      setLoading(false)
    }

    loadData()
  }, [topicId, activeSubtopic, activeSubtopicId, activeTab])

  // 格式化相對時間的函數
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return `${diffInSeconds}秒前`
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分鐘前`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小時前`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`
    if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)}個月前`
    return `${Math.floor(diffInSeconds / 31536000)}年前`
  }

  if (loading) {
    return activeTab === "viewpoints" ? (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="overflow-hidden h-full">
            <CardContent className="p-0">
              <div className="p-4">
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-2/3" />
              </div>
              <div className="border-t p-4 flex justify-between">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-4 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    ) : (
      <div className="space-y-4">
        <div className="grid grid-cols-12 text-sm font-medium border-b pb-2">
          <div className="col-span-7">主題</div>
          <div className="col-span-1 text-center">回覆</div>
          <div className="col-span-2 text-center">瀏覽</div>
          <div className="col-span-2 text-center">活動</div>
        </div>

        {[1, 2, 3, 4, 5].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardContent className="p-4">
              <div className="grid grid-cols-12 items-center gap-4">
                <div className="col-span-7 flex items-center gap-3">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="w-full">
                    <Skeleton className="h-5 w-3/4 mb-1" />
                    <Skeleton className="h-4 w-1/3" />
                  </div>
                </div>
                <div className="col-span-1 text-center">
                  <Skeleton className="h-6 w-12 mx-auto" />
                </div>
                <div className="col-span-2 text-center">
                  <Skeleton className="h-6 w-12 mx-auto" />
                </div>
                <div className="col-span-2 text-center">
                  <Skeleton className="h-6 w-16 mx-auto" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (activeTab === "viewpoints") {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {cards.length > 0 ? (
          cards.map((card) => (
            <div key={card.id} className="relative group h-full">
              <ContentCard {...card} />
            </div>
          ))
        ) : (
          <div className="col-span-2 text-center py-12">
            <p className="text-muted-foreground">暫無相關觀點卡</p>
          </div>
        )}
      </div>
    )
  } else {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-12 text-sm font-medium border-b pb-2">
          <div className="col-span-7">主題</div>
          <div className="col-span-1 text-center">回覆</div>
          <div className="col-span-2 text-center">瀏覽</div>
          <div className="col-span-2 text-center">活動</div>
        </div>

        {threads.length > 0 ? (
          threads.map((thread) => (
            <Card
              key={thread.id}
              className="overflow-hidden border-border/40 hover:border-border/80 transition-colors hover:shadow-md"
            >
              <CardContent className="p-4">
                <div className="grid grid-cols-12 items-center gap-4">
                  <div className="col-span-7 flex items-center gap-3">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback className="bg-primary/10 text-primary">
                        {thread.author.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <Link href={`/thread/${thread.id}`} className="font-medium hover:text-primary transition-colors">
                        {thread.title}
                      </Link>
                      <div className="text-sm text-muted-foreground">{thread.author}</div>
                    </div>
                  </div>
                  <div className="col-span-1 text-center flex justify-center">
                    <Badge variant="outline" className="flex items-center">
                      <MessageSquare className="h-3 w-3 mr-1" />
                      <span>{thread.replies}</span>
                    </Badge>
                  </div>
                  <div className="col-span-2 text-center flex justify-center">
                    <Badge variant="outline" className="flex items-center">
                      <Eye className="h-3 w-3 mr-1" />
                      <span>{thread.views >= 1000 ? `${(thread.views / 1000).toFixed(1)}k` : thread.views}</span>
                    </Badge>
                  </div>
                  <div className="col-span-2 text-center">
                    <Badge variant="secondary">{thread.lastActive}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">暫無相關討論</p>
          </div>
        )}
      </div>
    )
  }
}
