"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, Lock, Globe, Calendar, BookmarkIcon, GripVertical, Loader2, FolderOpen } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

interface Category {
  id: string
  name: string
}

interface CollectionCardProps {
  id: string | number
  name: string
  description?: string
  coverImage?: string
  itemCount: number
  isPublic: boolean
  createdAt: string
  updatedAt: string
  category?: string
  categoryId?: string
  onDrop: (itemId: number, collectionId: number) => void
  // 新增的 props
  categories?: Category[]
  onCategoryChange?: (collectionId: string, categoryId: string | null) => Promise<void>
}

export function CollectionCard({
  id,
  name,
  description,
  coverImage,
  itemCount,
  isPublic,
  createdAt,
  updatedAt,
  category,
  categoryId,
  onDrop,
  categories = [],
  onCategoryChange,
}: CollectionCardProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [isUpdatingCategory, setIsUpdatingCategory] = useState(false)
  const router = useRouter()
  const { toast } = useToast()
  const cardRef = useRef<HTMLDivElement>(null)

  // 格式化日期
  const formattedDate = new Date(updatedAt).toLocaleDateString("zh-TW", {
    year: "numeric",
    month: "short",
    day: "numeric",
  })

  // 處理拖拽進入
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  // 處理拖拽離開
  const handleDragLeave = () => {
    setIsDragOver(false)
  }

  // 處理放置
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    try {
      // 嘗試從拖拽數據中獲取 JSON
      const jsonData = e.dataTransfer.getData("application/json")
      if (!jsonData) {
        console.error("No JSON data found in drag event")
        return
      }

      const data = JSON.parse(jsonData)
      const { itemId } = data

      if (itemId) {
        // 確保 collectionId 是 number 類型
        const collectionId = typeof id === "string" ? parseInt(id) : id
        onDrop(itemId, collectionId)
      }
    } catch (err) {
      console.error("Failed to parse drag data:", err)
    }
  }

  // 處理卡片點擊
  const handleCardClick = () => {
    router.push(`/library/collection/${id}`)
  }

  return (
    <Card
      className={`overflow-hidden transition-all cursor-pointer ${isDragOver ? "ring-2 ring-primary scale-[1.02]" : ""}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={handleCardClick}
    >
      <div
        className="h-32 bg-cover bg-center"
        style={{ backgroundImage: `url(${coverImage || "/abstract-geometric-shapes.png"})` }}
      >
        <div className="flex justify-between items-start p-3 bg-gradient-to-b from-black/60 to-transparent">
          <Badge variant={isPublic ? "default" : "secondary"} className="flex items-center gap-1">
            {isPublic ? <Globe className="h-3 w-3" /> : <Lock className="h-3 w-3" />}
            {isPublic ? "公開" : "私人"}
          </Badge>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 bg-black/20 text-white hover:bg-black/40"
                onClick={(e) => e.stopPropagation()} // 防止觸發卡片點擊
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
              <DropdownMenuItem asChild>
                <Link href={`/library/collection/${id}`}>查看詳情</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/library/collection/${id}/edit`}>編輯收藏牆</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive">刪除收藏牆</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <CardHeader className="p-4 pb-0">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold text-lg line-clamp-1">{name}</h3>
            <p className="text-sm text-muted-foreground line-clamp-1">{category}</p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-2">
        {description && <p className="text-sm text-muted-foreground line-clamp-2 mb-2">{description}</p>}
      </CardContent>
      <CardFooter className="p-4 pt-0 flex justify-between items-center text-sm text-muted-foreground">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          <span>{formattedDate}</span>
        </div>
        <div className="flex items-center gap-2">
          <BookmarkIcon className="h-4 w-4" />
          <span>{itemCount} 項</span>
        </div>
      </CardFooter>
    </Card>
  )
}
