import { getSupabase, type ApiResponse, handleError, successResponse } from "./api-utils"
import type { Discussion } from "@/lib/types"

// 獲取主題相關的討論串
export async function getThreadsByTopic(topicId: string, limit = 10): Promise<ApiResponse<Discussion[]>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("thread_topics")
      .select(`
        thread_id,
        threads:thread_id (
          id,
          title,
          content,
          author_id,
          semantic_type,
          status,
          created_at,
          profiles:author_id (
            id,
            name,
            avatar
          )
        )
      `)
      .eq("topic_id", topicId)
      .limit(limit)

    if (error) throw error

    // 重新格式化數據以符合 Discussion 類型
    const threads = data.map((item) => ({
      id: item.threads.id,
      title: item.threads.title,
      content: item.threads.content,
      author_id: item.threads.author_id,
      semantic_type: item.threads.semantic_type,
      created_at: item.threads.created_at,
      author: item.threads.profiles,
    }))

    return successResponse(threads)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取討論串的評論數
export async function getThreadCommentCount(threadId: string): Promise<ApiResponse<number>> {
  try {
    const supabase = getSupabase()
    const { count, error } = await supabase
      .from("comments")
      .select("*", { count: "exact" })
      .eq("root_item_id", threadId)
      .eq("root_item_type", "thread")

    if (error) throw error

    return successResponse(count || 0)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取討論串的瀏覽數（假設有一個 thread_views 表）
export async function getThreadViewCount(threadId: string): Promise<ApiResponse<number>> {
  try {
    // 這裡假設有一個 thread_views 表來記錄瀏覽數
    // 如果沒有，可以返回一個隨機數或固定值作為示例
    const supabase = getSupabase()
    const { count, error } = await supabase
      .from("thread_views")
      .select("*", { count: "exact" })
      .eq("thread_id", threadId)

    if (error) {
      // 如果表不存在，返回一個隨機數作為示例
      return successResponse(Math.floor(Math.random() * 1000))
    }

    return successResponse(count || 0)
  } catch (error) {
    // 如果出錯，返回一個隨機數作為示例
    return successResponse(Math.floor(Math.random() * 1000))
  }
}
