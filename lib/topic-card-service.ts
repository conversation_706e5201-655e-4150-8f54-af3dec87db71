import { getSupabase, type ApiResponse, handleError, successResponse } from "./api-utils"
import type { Card } from "@/lib/types"

// 獲取主題相關的卡片
export async function getCardsByTopic(topicId: string, limit = 10): Promise<ApiResponse<Card[]>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("card_topics")
      .select(`
        card_id,
        cards:card_id (
          id,
          title,
          content,
          author_id,
          card_type,
          semantic_type,
          contribution_type,
          original_author,
          original_url,
          created_at,
          updated_at,
          profiles:author_id (
            id,
            name,
            avatar
          )
        )
      `)
      .eq("topic_id", topicId)
      .limit(limit)

    if (error) throw error

    // 重新格式化數據以符合 Card 類型
    const cards = data.map((item) => ({
      id: item.cards.id,
      title: item.cards.title,
      content: item.cards.content,
      author_id: item.cards.author_id,
      card_type: item.cards.card_type,
      semantic_type: item.cards.semantic_type,
      contribution_type: item.cards.contribution_type,
      original_author: item.cards.original_author,
      original_url: item.cards.original_url,
      created_at: item.cards.created_at,
      updated_at: item.cards.updated_at,
      author: item.cards.profiles,
    }))

    // 獲取每張卡片的主題和子主題
    for (const card of cards) {
      // 獲取卡片的主題
      const { data: topicData, error: topicError } = await supabase
        .from("card_topics")
        .select(`
          topics:topic_id (
            id,
            name,
            slug
          )
        `)
        .eq("card_id", card.id)

      if (topicError) throw topicError

      // 獲取卡片的子主題
      const { data: subtopicData, error: subtopicError } = await supabase
        .from("card_subtopics")
        .select(`
          subtopics:subtopic_id (
            id,
            name,
            slug
          )
        `)
        .eq("card_id", card.id)

      if (subtopicError) throw subtopicError

      // 添加主題和子主題到卡片
      card.topics = topicData.map((t) => t.topics)
      card.subtopics = subtopicData.map((s) => s.subtopics)
    }

    return successResponse(cards)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取子主題相關的卡片
export async function getCardsBySubtopic(subtopicId: string, limit = 10): Promise<ApiResponse<Card[]>> {
  try {
    const supabase = getSupabase()
    const { data, error } = await supabase
      .from("card_subtopics")
      .select(`
        card_id,
        cards:card_id (
          id,
          title,
          content,
          author_id,
          card_type,
          semantic_type,
          contribution_type,
          original_author,
          original_url,
          created_at,
          updated_at,
          profiles:author_id (
            id,
            name,
            avatar
          )
        )
      `)
      .eq("subtopic_id", subtopicId)
      .limit(limit)

    if (error) throw error

    // 重新格式化數據以符合 Card 類型
    const cards = data.map((item) => ({
      id: item.cards.id,
      title: item.cards.title,
      content: item.cards.content,
      author_id: item.cards.author_id,
      card_type: item.cards.card_type,
      semantic_type: item.cards.semantic_type,
      contribution_type: item.cards.contribution_type,
      original_author: item.cards.original_author,
      original_url: item.cards.original_url,
      created_at: item.cards.created_at,
      updated_at: item.cards.updated_at,
      author: item.cards.profiles,
    }))

    // 獲取每張卡片的主題和子主題
    for (const card of cards) {
      // 獲取卡片的主題
      const { data: topicData, error: topicError } = await supabase
        .from("card_topics")
        .select(`
          topics:topic_id (
            id,
            name,
            slug
          )
        `)
        .eq("card_id", card.id)

      if (topicError) throw topicError

      // 獲取卡片的子主題
      const { data: subtopicData, error: subtopicError } = await supabase
        .from("card_subtopics")
        .select(`
          subtopics:subtopic_id (
            id,
            name,
            slug
          )
        `)
        .eq("card_id", card.id)

      if (subtopicError) throw subtopicError

      // 添加主題和子主題到卡片
      card.topics = topicData.map((t) => t.topics)
      card.subtopics = subtopicData.map((s) => s.subtopics)
    }

    return successResponse(cards)
  } catch (error) {
    return handleError(error)
  }
}

// 獲取卡片的互動統計
export async function getCardStats(cardId: string): Promise<
  ApiResponse<{
    likes: number
    dislikes: number
    comments: number
    bookmarks: number
  }>
> {
  try {
    const supabase = getSupabase()

    // 獲取點讚數
    const { count: likesCount, error: likesError } = await supabase
      .from("reactions")
      .select("*", { count: "exact" })
      .eq("item_id", cardId)
      .eq("item_type", "card")
      .eq("reaction_type", "like")

    if (likesError) throw likesError

    // 獲取不喜歡數
    const { count: dislikesCount, error: dislikesError } = await supabase
      .from("reactions")
      .select("*", { count: "exact" })
      .eq("item_id", cardId)
      .eq("item_type", "card")
      .eq("reaction_type", "dislike")

    if (dislikesError) throw dislikesError

    // 獲取評論數
    const { count: commentsCount, error: commentsError } = await supabase
      .from("comments")
      .select("*", { count: "exact" })
      .eq("root_item_id", cardId)
      .eq("root_item_type", "card")

    if (commentsError) throw commentsError

    // 獲取收藏數
    const { count: bookmarksCount, error: bookmarksError } = await supabase
      .from("bookmarks")
      .select("*", { count: "exact" })
      .eq("item_id", cardId)
      .eq("item_type", "card")

    if (bookmarksError) throw bookmarksError

    return successResponse({
      likes: likesCount || 0,
      dislikes: dislikesCount || 0,
      comments: commentsCount || 0,
      bookmarks: bookmarksCount || 0,
    })
  } catch (error) {
    return handleError(error)
  }
}
