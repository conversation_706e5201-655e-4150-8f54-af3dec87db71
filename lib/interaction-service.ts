import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"

// 添加評論
export async function addComment({
  itemType,
  itemId,
  content,
  parentCommentId = null,
  referencedCard = null,
}: {
  itemType: "card" | "thread"
  itemId: number | string
  content: string
  parentCommentId?: number | null
  referencedCard?: { id: number | string; type: string } | null
}) {
  try {
    const supabase = createClientComponentClient()

    // 獲取當前用戶
    const { data: userData, error: userError } = await supabase.auth.getUser()
    if (userError || !userData.user) {
      console.error("Error getting user:", userError)
      return { success: false, error: "未登入或獲取用戶信息失敗" }
    }

    const userId = userData.user.id

    // 創建評論 - 使用正確的列名
    const { data: commentData, error: commentError } = await supabase
      .from("comments")
      .insert({
        root_item_type: itemType,
        root_item_id: itemId,
        content: content,
        author_id: userId,
        parent_comment_id: parentCommentId,
      })
      .select("id, created_at")
      .single()

    if (commentError) {
      console.error("Error creating comment:", commentError)
      return { success: false, error: "創建評論失敗: " + commentError.message }
    }

    // 如果有引用的卡片，添加到 CONTENT_REFERENCES 表
    if (referencedCard && commentData) {
      const { error: refError } = await supabase.from("content_references").insert({
        source_type: "comment",
        source_id: commentData.id,
        target_type: "card",
        target_id: referencedCard.id,
        reference_type: referencedCard.type || "quote",
        created_at: new Date().toISOString(),
      })

      if (refError) {
        console.error("Error creating content reference:", refError)
        // 不要因為引用創建失敗而阻止評論創建
        // 但是記錄錯誤
      }
    }

    // 獲取用戶信息
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("name, avatar")
      .eq("id", userId)
      .single()

    if (profileError) {
      console.error("Error getting profile:", profileError)
      return { success: false, error: "獲取用戶信息失敗" }
    }

    // 如果有引用的卡片，獲取卡片信息用於返回
    let referencedCardData = null
    if (referencedCard) {
      const { data: cardData, error: cardError } = await supabase
        .from("cards")
        .select(`
          id, 
          title, 
          content, 
          semantic_type, 
          topics (id, name),
          subtopics (id, name),
          profiles (id, name, avatar)
        `)
        .eq("id", referencedCard.id)
        .single()

      if (cardError) {
        console.error("Error getting referenced card:", cardError)
      } else {
        referencedCardData = {
          id: cardData.id,
          title: cardData.title,
          content: cardData.content,
          type: cardData.semantic_type,
          author: cardData.profiles?.name || "未知作者",
          tags: cardData.subtopics ? cardData.subtopics.map((s: any) => s.name) : [],
          topics: cardData.topics || [],
        }
      }
    }

    // 返回評論數據
    return {
      success: true,
      data: {
        id: commentData.id,
        content,
        created_at: commentData.created_at,
        parent_comment_id: parentCommentId,
        author: {
          name: profileData.name,
          avatar: profileData.avatar,
        },
        referenced_card: referencedCardData,
      },
    }
  } catch (error) {
    console.error("Error in addComment:", error)
    return { success: false, error: "添加評論時發生錯誤" }
  }
}

// 獲取評論的引用卡片
export async function getCommentReferences(commentIds: string[] | number[]) {
  if (!commentIds.length) return { success: true, data: {} }

  try {
    const supabase = createClientComponentClient()

    // 獲取所有評論的引用
    const { data: references, error: refError } = await supabase
      .from("content_references")
      .select("*")
      .eq("source_type", "comment")
      .in("source_id", commentIds)
      .eq("target_type", "card")

    if (refError) {
      console.error("Error fetching comment references:", refError)
      return { success: false, error: "獲取評論引用失敗" }
    }

    // 如果沒有引用，返回空對象
    if (!references || references.length === 0) {
      return { success: true, data: {} }
    }

    // 獲取所有引用的卡片 ID
    const cardIds = references.map((ref) => ref.target_id)

    // 獲取卡片詳情
    const { data: cards, error: cardError } = await supabase
      .from("cards")
      .select(`
        id, 
        title, 
        content, 
        semantic_type, 
        topics (id, name),
        subtopics (id, name),
        profiles (id, name, avatar)
      `)
      .in("id", cardIds)

    if (cardError) {
      console.error("Error fetching referenced cards:", cardError)
      return { success: false, error: "獲取引用卡片失敗" }
    }

    // 構建評論 ID 到引用卡片的映射
    const commentReferences: Record<string, any> = {}

    references.forEach((ref) => {
      const card = cards.find((c) => c.id === ref.target_id)
      if (card) {
        const cardData = {
          id: card.id,
          title: card.title,
          content: card.content,
          type: card.semantic_type,
          author: card.profiles?.name || "未知作者",
          tags: card.subtopics ? card.subtopics.map((s: any) => s.name) : [],
          topics: card.topics || [],
          reference_type: ref.reference_type,
        }

        commentReferences[ref.source_id] = cardData
      }
    })

    return { success: true, data: commentReferences }
  } catch (error) {
    console.error("Error in getCommentReferences:", error)
    return { success: false, error: "獲取評論引用時發生錯誤" }
  }
}
