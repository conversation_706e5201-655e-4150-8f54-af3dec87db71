import { createServerClient as createClient } from "@supabase/ssr"
import { cookies } from "next/headers"
import type { CookieOptions } from "@supabase/ssr"

export function createServerClient(cookieStore?: ReturnType<typeof cookies>) {
  const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey =
    process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    throw new Error("Missing Supabase environment variables")
  }

  // 如果沒有提供 cookieStore，則嘗試獲取
  const cookieStoreToUse = cookieStore || cookies()

  return createClient(supabaseUrl, supabaseKey, {
    cookies: {
      get(name: string) {
        return cookieStoreToUse.get(name)?.value
      },
      set(name: string, value: string, options: CookieOptions) {
        try {
          cookieStoreToUse.set({ name, value, ...options })
        } catch (error) {
          // 在某些情況下，cookies 可能是只讀的
          console.error("Error setting cookie:", error)
        }
      },
      remove(name: string, options: CookieOptions) {
        try {
          cookieStoreToUse.set({ name, value: "", ...options })
        } catch (error) {
          // 在某些情況下，cookies 可能是只讀的
          console.error("Error removing cookie:", error)
        }
      },
    },
  })
}
