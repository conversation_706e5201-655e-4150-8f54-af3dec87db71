import { createClient } from "@supabase/supabase-js"

// 使用單例模式確保只創建一個客戶端實例
let supabaseClient: ReturnType<typeof createClient> | null = null

// 保持原來的函數名稱 createBrowserClient
export function createBrowserClient() {
  if (supabaseClient) return supabaseClient

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error(
      "Missing Supabase environment variables. Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY.",
    )
    // 提供一個默認值，避免運行時錯誤
    return createClient("https://your-project.supabase.co", "your-anon-key")
  }

  supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
  })

  return supabaseClient
}

// 為了向後兼容，保留 supabase 實例
export const supabase = createBrowserClient()
