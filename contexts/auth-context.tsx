"use client"

import type React from "react"

import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { useRouter, usePathname } from "next/navigation"
import { createContext, useContext, useEffect, useState, useMemo, useRef, useCallback } from "react"

type Profile = {
  id: string
  name: string
  email: string
  avatar_url?: string
  bio?: string
  created_at?: string
  updated_at?: string
}

type ProfileUpdatePayload = {
  name?: string
  bio?: string
  avatar_url?: string
}

type User = {
  id: string
  email: string
}

type AuthContextType = {
  user: User | null
  profile: Profile | null
  isAuthenticated: boolean
  isLoading: boolean
  signUp: (email: string, password: string, name: string) => Promise<{ error: Error | null }>
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>
  signInWithProvider: (provider: "google" | "facebook" | "github") => Promise<void>
  signOut: () => Promise<void>
  refreshProfile: () => Promise<void>
  updateProfile: (data: ProfileUpdatePayload) => Promise<void>
  refreshSession: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  profile: null,
  isAuthenticated: false,
  isLoading: true,
  signUp: async () => ({ error: null }),
  signIn: async () => ({ error: null }),
  signInWithProvider: async () => {},
  signOut: async () => {},
  refreshProfile: async () => {},
  updateProfile: async () => {},
  refreshSession: async () => {},
})

export const useAuth = () => useContext(AuthContext)

// 實現更高效的請求節流
const throttleRequest = (() => {
  const timestamps: Record<string, number> = {}
  const MIN_INTERVAL = 5000 // 增加最小間隔時間（毫秒）

  return (key: string): boolean => {
    const now = Date.now()
    if (!timestamps[key] || now - timestamps[key] > MIN_INTERVAL) {
      timestamps[key] = now
      return true
    }
    return false
  }
})()

// 本地存儲鍵
const AUTH_STATE_KEY = "ailogora_auth_state"

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [authError, setAuthError] = useState<Error | null>(null)
  const router = useRouter()
  const pathname = usePathname()

  // 使用 ref 來追蹤 profile 請求狀態，避免重複請求
  const profileRequestInProgress = useRef<Record<string, boolean>>({})
  const retryCount = useRef<Record<string, number>>({})
  const MAX_RETRIES = 3
  const lastPathRef = useRef<string | null>(null)

  // 添加一個 ref 來追蹤初始化狀態
  const isInitialized = useRef(false)

  // 添加一個 ref 來追蹤最後一次獲取 profile 的時間
  const lastProfileFetchTime = useRef<Record<string, number>>({})

  // 優化：使用 useMemo 緩存 supabase 客戶端
  const supabaseClient = useMemo(() => createClientComponentClient(), [])

  // 從本地存儲恢復認證狀態
  const restoreAuthState = useCallback(() => {
    try {
      const storedState = localStorage.getItem(AUTH_STATE_KEY)
      if (storedState) {
        const { user: storedUser, isAuthenticated: storedIsAuth } = JSON.parse(storedState)
        if (storedUser && storedIsAuth) {
          setUser(storedUser)
          setIsAuthenticated(true)
          return true
        }
      }
    } catch (error) {
      console.error("Error restoring auth state:", error)
    }
    return false
  }, [])

  // 保存認證狀態到本地存儲
  const saveAuthState = useCallback((currentUser: User | null, isAuth: boolean) => {
    try {
      if (currentUser && isAuth) {
        localStorage.setItem(
          AUTH_STATE_KEY,
          JSON.stringify({
            user: currentUser,
            isAuthenticated: isAuth,
          }),
        )
      } else {
        localStorage.removeItem(AUTH_STATE_KEY)
      }
    } catch (error) {
      console.error("Error saving auth state:", error)
    }
  }, [])

  // 刷新會話
  const refreshSession = useCallback(async () => {
    try {
      // 避免重複刷新
      if (!throttleRequest("refreshSession")) {
        return false
      }

      setIsLoading(true)
      console.log("Refreshing session...")

      const { data, error } = await supabaseClient.auth.getSession()

      if (error) {
        throw error
      }

      if (data.session) {
        const currentUser = {
          id: data.session.user.id,
          email: data.session.user.email || "",
        }
        setUser(currentUser)
        setIsAuthenticated(true)
        saveAuthState(currentUser, true)

        // 只有在沒有 profile 或者 profile.id 不匹配時才獲取 profile
        if (!profile || profile.id !== data.session.user.id) {
          // 異步獲取用戶資料
          fetchProfile(data.session.user.id).catch((err) => {
            console.error("Error fetching profile during session refresh:", err)
          })
        }

        return true
      } else {
        setUser(null)
        setProfile(null)
        setIsAuthenticated(false)
        saveAuthState(null, false)
        return false
      }
    } catch (error) {
      console.error("Error refreshing session:", error)
      return false
    } finally {
      setIsLoading(false)
    }
  }, [supabaseClient, saveAuthState, profile])

  // 修改 checkSession 函數，確保超時處理和錯誤恢復
  const checkSession = useCallback(
    async (force = false) => {
      // 如果不是強制檢查，且已經有認證狀態，則跳過
      if (!force && isAuthenticated && user) {
        return true
      }

      // 檢查是否需要節流請求
      if (!force && !throttleRequest("checkSession")) {
        console.log("Session check throttled")
        return false
      }

      try {
        console.log("Checking session...")
        setIsLoading(true)

        // 嘗試從本地存儲恢復狀態
        const restored = restoreAuthState()
        if (restored && !force) {
          console.log("Auth state restored from local storage")
          // 即使恢復了狀態，也異步檢查會話有效性
          refreshSession().catch(console.error)
          return true
        }

        // 設置請求超時
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error("Session check timeout")), 8000),
        )

        const sessionPromise = supabaseClient.auth.getSession()

        // 使用 Promise.race 確保請求不會無限等待
        const { data } = (await Promise.race([sessionPromise, timeoutPromise])) as { data: { session: any } }
        console.log("Session check complete:", !!data.session)

        if (data.session) {
          const currentUser = {
            id: data.session.user.id,
            email: data.session.user.email || "",
          }
          setUser(currentUser)
          setIsAuthenticated(true)
          saveAuthState(currentUser, true)

          // 只有在沒有 profile 或者 profile.id 不匹配時才獲取 profile
          if (!profile || profile.id !== data.session.user.id) {
            // 如果有會話，異步加載用戶資料
            fetchProfile(data.session.user.id).catch((err) => {
              console.error("Error fetching profile:", err)
              // 即使獲取資料失敗，也不影響認證狀態
            })
          }
          return true
        } else {
          setUser(null)
          setProfile(null)
          setIsAuthenticated(false)
          saveAuthState(null, false)
          return false
        }
      } catch (error) {
        console.error("Error checking auth session:", error)

        // 出錯時嘗試從本地存儲恢復
        const restored = restoreAuthState()
        if (restored) {
          console.log("Auth state restored after error")
          return true
        }

        // 出錯時嘗試再次檢查會話，但不要無限重試
        if (!authError) {
          setAuthError(error as Error)
          // 延遲後再次嘗試，但只嘗試一次
          setTimeout(() => {
            supabaseClient.auth
              .getSession()
              .then(({ data }) => {
                if (data.session) {
                  const currentUser = {
                    id: data.session.user.id,
                    email: data.session.user.email || "",
                  }
                  setUser(currentUser)
                  setIsAuthenticated(true)
                  saveAuthState(currentUser, true)
                  setAuthError(null)
                } else {
                  setUser(null)
                  setProfile(null)
                  setIsAuthenticated(false)
                  saveAuthState(null, false)
                }
                setIsLoading(false)
              })
              .catch(() => {
                setIsLoading(false)
              })
          }, 1000)
          return false
        }

        // 出錯時設置為未認證狀態
        setUser(null)
        setProfile(null)
        setIsAuthenticated(false)
        saveAuthState(null, false)
        return false
      } finally {
        // 無論結果如何，都結束加載狀態
        setIsLoading(false)
      }
    },
    [isAuthenticated, user, restoreAuthState, refreshSession, supabaseClient, saveAuthState, profile],
  )

  // 監聽路由變化
  useEffect(() => {
    if (pathname !== lastPathRef.current) {
      console.log("Path changed from", lastPathRef.current, "to", pathname)
      lastPathRef.current = pathname

      // 路由變化時檢查會話狀態，但避免頻繁檢查
      if (isAuthenticated && throttleRequest(`pathChange-${pathname}`)) {
        // 如果已認證，異步刷新會話
        refreshSession().catch(console.error)
      } else if (!isAuthenticated && throttleRequest(`pathChange-${pathname}`)) {
        // 如果未認證，檢查會話
        checkSession().catch(console.error)
      }
    }
  }, [pathname, isAuthenticated, refreshSession, checkSession])

  // 修改 useEffect 中的 onAuthStateChange 處理
  useEffect(() => {
    // 防止重複初始化
    if (isInitialized.current) return

    isInitialized.current = true

    // 初始檢查會話
    checkSession()

    // 監聽身份驗證狀態變化
    const {
      data: { subscription },
    } = supabaseClient.auth.onAuthStateChange(async (event, session) => {
      console.log("Auth state changed:", event)

      // 對於特定事件，設置加載狀態
      if (["SIGNED_IN", "SIGNED_OUT", "TOKEN_REFRESHED", "USER_UPDATED"].includes(event)) {
        setIsLoading(true)
      }

      if (session) {
        // 用戶已登入
        const currentUser = {
          id: session.user.id,
          email: session.user.email || "",
        }

        // 避免重複設置相同的用戶
        if (!user || user.id !== currentUser.id) {
          setUser(currentUser)
          setIsAuthenticated(true)
          saveAuthState(currentUser, true)
          setAuthError(null)

          // 只有在沒有 profile 或者 profile.id 不匹配時才獲取 profile
          if (!profile || profile.id !== currentUser.id) {
            // 異步獲取用戶資料，不阻塞 UI
            fetchProfile(session.user.id).catch((err) => {
              console.error("Error fetching profile during auth change:", err)
            })
          }
        }
      } else if (event === "SIGNED_OUT") {
        // 用戶已登出
        setUser(null)
        setProfile(null)
        setIsAuthenticated(false)
        saveAuthState(null, false)
      }

      // 確保加載狀態結束
      setIsLoading(false)
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [supabaseClient, checkSession, saveAuthState, user, profile])

  // 實現指數退避算法
  const getBackoffTime = (retryAttempt: number): number => {
    return Math.min(1000 * 2 ** retryAttempt, 30000) // 最大 30 秒
  }

  // 優化：分離獲取用戶資料的邏輯，減少主要效果的複雜性
  const fetchProfile = async (userId: string) => {
    // 避免重複請求
    if (profileRequestInProgress.current[userId]) {
      console.log("Profile request already in progress for user:", userId)
      return
    }

    // 檢查是否需要節流請求 - 增加時間間隔
    const now = Date.now()
    const lastFetchTime = lastProfileFetchTime.current[userId] || 0
    if (now - lastFetchTime < 10000) {
      // 10 秒內不重複獲取
      console.log("Profile fetch throttled for user:", userId)
      return
    }

    // 更新最後獲取時間
    lastProfileFetchTime.current[userId] = now

    try {
      profileRequestInProgress.current[userId] = true
      console.log("Fetching profile for user:", userId)

      // 添加緩存控制，避免頻繁請求
      const { data, error } = await supabaseClient
        .from("profiles")
        .select("*") // 選擇所有字段
        .eq("id", userId)
        .single()

      // 重置重試計數
      retryCount.current[userId] = 0

      if (error) {
        console.error("Error fetching profile:", error)
        // 如果找不到資料，嘗試從 auth 用戶信息創建基本資料
        const { data: userData } = await supabaseClient.auth.getUser()
        if (userData?.user) {
          console.log("Creating new profile for user:", userId)
          // 創建基本資料
          const { data: newProfile, error: insertError } = await supabaseClient
            .from("profiles")
            .insert({
              id: userId,
              name: userData.user.user_metadata?.name || "用戶",
              email: userData.user.email,
            })
            .select()
            .single()

          if (insertError) {
            console.error("Error creating profile:", insertError)
            return
          }

          setProfile(newProfile as Profile)
          return
        }
      } else if (data) {
        console.log("Profile found:", data)
        setProfile(data as Profile)
        return
      }
    } catch (error: any) {
      console.error("Error in fetchProfile:", error)

      // 處理 "Too Many Requests" 錯誤
      if (
        error.message &&
        (error.message.includes("Too Many Requests") ||
          error.message.includes("429") ||
          error.message.includes("Unexpected token"))
      ) {
        const currentRetry = retryCount.current[userId] || 0
        if (currentRetry < MAX_RETRIES) {
          retryCount.current[userId] = currentRetry + 1
          const backoffTime = getBackoffTime(currentRetry)
          console.log(`Rate limited. Retrying in ${backoffTime}ms (attempt ${currentRetry + 1}/${MAX_RETRIES})`)

          // 使用指數退避算法重試
          setTimeout(() => {
            profileRequestInProgress.current[userId] = false
            fetchProfile(userId).catch(console.error)
          }, backoffTime)
        } else {
          console.error(`Max retries (${MAX_RETRIES}) reached for fetching profile`)
        }
      }
    } finally {
      // 延遲一段時間後才允許下一次請求
      setTimeout(() => {
        profileRequestInProgress.current[userId] = false
      }, 5000) // 增加到 5 秒
    }
  }

  const refreshProfile = async () => {
    if (!isAuthenticated || !user) return

    // 檢查是否需要節流請求
    if (!throttleRequest(`refreshProfile-${user.id}`)) {
      console.log("Profile refresh throttled")
      return
    }

    await fetchProfile(user.id)
  }

  const updateProfile = async (data: ProfileUpdatePayload) => {
    if (!isAuthenticated || !user) {
      throw new Error("User not authenticated")
    }

    try {
      const { error } = await supabaseClient
        .from("profiles")
        .update({
          ...data,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id)

      if (error) {
        throw error
      }

      // 更新成功後刷新資料
      await refreshProfile()
    } catch (error) {
      console.error("Error updating profile:", error)
      throw error
    }
  }

  // 優化 signOut 函數以提高性能
  const signOut = async () => {
    try {
      // 設置為加載狀態
      setIsLoading(true)

      // 執行登出操作
      const { error } = await supabaseClient.auth.signOut()

      if (error) {
        throw error
      }

      // 登出成功後更新狀態
      setUser(null)
      setProfile(null)
      setIsAuthenticated(false)
      saveAuthState(null, false)

      // 重定向到首頁
      router.push("/")
    } catch (error) {
      console.error("Error signing out:", error)
    } finally {
      // 無論成功或失敗，都結束加載狀態
      setIsLoading(false)
    }
  }

  const signUp = async (email: string, password: string, name: string) => {
    try {
      const { data, error } = await supabaseClient.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      })

      if (error) {
        console.error("Error signing up:", error)
        return { error }
      }

      // Automatically sign in after sign up
      if (data.user) {
        const currentUser = {
          id: data.user.id,
          email: data.user.email || "",
        }
        setUser(currentUser)
        setIsAuthenticated(true)
        saveAuthState(currentUser, true)
        // Profile will be created by the trigger
        return { error: null }
      }

      return { error: null }
    } catch (error) {
      console.error("Error in signUp:", error)
      return { error: error as Error }
    }
  }

  const signInWithProvider = async (provider: "google" | "facebook" | "github") => {
    try {
      const { error } = await supabaseClient.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        console.error(`Error signing in with ${provider}:`, error)
      }
    } catch (error) {
      console.error(`Error in signInWithProvider (${provider}):`, error)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabaseClient.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        console.error("Error signing in:", error)
        return { error }
      }

      if (data.user) {
        const currentUser = {
          id: data.user.id,
          email: data.user.email || "",
        }
        setUser(currentUser)
        setIsAuthenticated(true)
        saveAuthState(currentUser, true)

        // 只有在沒有 profile 或者 profile.id 不匹配時才獲取 profile
        if (!profile || profile.id !== data.user.id) {
          await fetchProfile(data.user.id)
        }

        return { error: null }
      }

      return { error: null }
    } catch (error) {
      console.error("Error in signIn:", error)
      return { error: error as Error }
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        profile,
        isAuthenticated,
        isLoading,
        signUp,
        signIn,
        signInWithProvider,
        signOut,
        refreshProfile,
        updateProfile,
        refreshSession,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
